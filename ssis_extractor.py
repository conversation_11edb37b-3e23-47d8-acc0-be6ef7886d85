import re
from typing import Optional, Dict, Any
from dataclasses import dataclass
from universal_azure_price_extractor import BaseProductExtractor, ProductInstance, PriceDetail

@dataclass
class HybridBenefit:
    """混合优惠信息"""
    price: PriceDetail
    savings_percentage: int

@dataclass
class SSISInstance(ProductInstance):
    """SSIS虚拟机实例"""
    vcores: Optional[int] = None
    memory_gb: Optional[float] = None
    temp_storage: Optional[str] = None
    hybrid_benefit: Optional[HybridBenefit] = None
    
    def __post_init__(self):
        # 确保specifications字典包含所有字段
        if not hasattr(self, 'specifications') or self.specifications is None:
            self.specifications = {}
        
        self.specifications.update({
            'vcores': self.vcores,
            'memory_gb': self.memory_gb,
            'temp_storage': self.temp_storage,
            'has_hybrid_benefit': self.hybrid_benefit is not None
        })

class SSISExtractor(BaseProductExtractor):
    """Data Factory SSIS 价格提取器"""
    
    def __init__(self):
        super().__init__()
        self.percentage_pattern = r'（~(\d+)%）'
    
    def get_product_name(self) -> str:
        return "Data Factory SSIS"
    
    def get_table_id_pattern(self) -> str:
        return r"data-factory-ssis"
    
    def parse_instance_from_row(self, row) -> Optional[ProductInstance]:
        """解析SSIS虚拟机实例行"""
        try:
            cells = row.find_all('td')
            if len(cells) < 5:
                return None
                
            name = cells[0].get_text(strip=True)
            vcores = int(cells[1].get_text(strip=True))
            
            memory_text = cells[2].get_text(strip=True)
            memory_gb = float(re.search(r'([\d\.]+)', memory_text).group(1))
            
            temp_storage = cells[3].get_text(strip=True)
            
            license_price = self.extract_price_from_text(cells[4].get_text())
            if not license_price:
                return None
                
            hybrid_benefit = None
            if len(cells) > 5:
                hybrid_benefit = self.extract_hybrid_benefit(cells[5].get_text())
            
            return SSISInstance(
                name=name,
                price=license_price,
                vcores=vcores,
                memory_gb=memory_gb,
                temp_storage=temp_storage,
                hybrid_benefit=hybrid_benefit,
                specifications={}
            )
        except Exception as e:
            print(f"SSIS实例解析错误: {e}")
            return None
    
    def extract_hybrid_benefit(self, benefit_text: str) -> Optional[HybridBenefit]:
        """提取混合优惠信息"""
        try:
            price = self.extract_price_from_text(benefit_text)
            if not price:
                return None
                
            percentage_match = re.search(self.percentage_pattern, benefit_text)
            savings_percentage = int(percentage_match.group(1)) if percentage_match else 0
            
            return HybridBenefit(
                price=price,
                savings_percentage=savings_percentage
            )
        except Exception as e:
            print(f"混合优惠解析错误: {benefit_text}, 错误: {e}")
            return None
    
    def extract_table_metadata(self, table, table_id: str) -> Dict[str, str]:
        """提取SSIS表格元数据"""
        try:
            # 查找表格标题
            title_element = table.find_previous('h3')
            title = title_element.get_text(strip=True) if title_element else ""
            
            # 解析系列和许可类型
            vm_series = self._extract_vm_series(title)
            license_type = "Enterprise" if "企业" in title else "Standard"
            
            return {
                'title': title,
                'category': f"SSIS {vm_series}",
                'service_tier': license_type
            }
            
        except Exception as e:
            print(f"SSIS元数据提取错误: {e}")
            return {'title': '', 'category': '', 'service_tier': ''}
    
    def _extract_vm_series(self, title: str) -> str:
        """从标题中提取VM系列"""
        series_patterns = [
            r'(Av2)', r'(Dv2)', r'(Dv3)', r'(Ev3)', r'(E.*v4)'
        ]
        for pattern in series_patterns:
            match = re.search(pattern, title)
            if match:
                return match.group(1)
        return "Unknown"
    
    def extract_price_from_text(self, price_text: str) -> Optional[PriceDetail]:
        """SSIS特定的价格解析"""
        try:
            hourly_match = re.search(r'￥([\d,\.]+)/小时', price_text)
            if not hourly_match:
                return None
            hourly_price = float(hourly_match.group(1).replace(',', ''))
            
            monthly_match = re.search(r'约￥([\d,\.]+)/月', price_text)
            monthly_price = float(monthly_match.group(1).replace(',', '')) if monthly_match else hourly_price * 24 * 31
            
            return PriceDetail(
                hourly_price=hourly_price,
                monthly_price=monthly_price,
                currency="CNY"
            )
        except Exception as e:
            print(f"SSIS价格解析错误: {price_text}, 错误: {e}")
            return None

# 使用示例
def main():
    """SSIS提取器使用示例"""
    extractor = SSISExtractor()
    
    # 读取HTML文件
    with open('ssis-index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 提取所有区域的定价数据
    print("=== 开始提取SSIS区域定价数据 ===")
    analysis = extractor.extract_all_regional_pricing(html_content, 'soft-category.json')
    
    # 输出结果
    print(f"\n=== 提取完成 ===")
    print(f"总区域数: {analysis.total_regions}")
    print(f"完整服务区域: {analysis.full_service_regions}个")
    print(f"受限服务区域: {analysis.partial_service_regions}个")
    
    print(f"\n=== 区域服务级别 ===")
    for region in analysis.regional_data:
        print(f"{region.region_name}: {region.service_level}")
        print(f"  └─ {region.config_status}")
        print(f"  └─ 可用表格: {len(region.available_tables)}个")
        print(f"  └─ 实例配置: {region.total_available_configurations}个")

if __name__ == "__main__":
    main()
