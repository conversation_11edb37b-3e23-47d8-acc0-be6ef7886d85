#!/usr/bin/env python3
"""
灵活架构演示 - 展示如何处理不同类型的HTML页面
"""

import os
import json
from typing import Dict, List, Any
from datetime import datetime

from ai_enhanced_extractor import AdaptiveExtractor, AITableAnalyzer
from unified_extraction_framework import UnifiedExtractionFramework

class FlexibleArchitectureDemo:
    """灵活架构演示"""
    
    def __init__(self):
        self.framework = UnifiedExtractionFramework()
        self.adaptive_extractor = AdaptiveExtractor()
        self.ai_analyzer = AITableAnalyzer()
    
    def demo_schema_evolution(self):
        """演示schema演进过程"""
        print("🔄 Schema演进演示")
        print("=" * 60)
        
        # 1. 处理已知产品（使用schema）
        print("\n1️⃣ 处理已知产品 (Schema-based)")
        known_products = [
            ('ssis-index.html', 'Data Factory SSIS'),
            ('mysql-index.html', 'Azure Database for MySQL')
        ]
        
        for html_file, product_name in known_products:
            if os.path.exists(html_file):
                with open(html_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                result = self.framework.extract_product_data(html_content, product_name, 'schema_first')
                print(f"  {product_name}:")
                print(f"    方法: {result.extraction_method}")
                print(f"    成功: {result.success}")
                print(f"    表格数: {len(result.tables)}")
        
        # 2. 处理未知产品（自适应）
        print("\n2️⃣ 处理未知产品 (Adaptive)")
        if os.path.exists('mysql-index.html'):
            with open('mysql-index.html', 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 假设这是一个全新的产品
            result = self.adaptive_extractor.extract_unknown_product(html_content, "Unknown Database Product")
            print(f"  Unknown Database Product:")
            print(f"    发现表格: {result['total_tables_found']}")
            print(f"    成功提取: {result['successfully_extracted']}")
            
            # 生成新的schema
            if result['successfully_extracted'] > 0:
                print(f"    ✓ 可以生成新的schema配置")
    
    def demo_ai_analysis(self):
        """演示AI分析能力"""
        print("\n🤖 AI分析能力演示")
        print("=" * 60)
        
        if not os.path.exists('mysql-index.html'):
            print("❌ 需要mysql-index.html文件")
            return
        
        with open('mysql-index.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 分析前5个表格
        tables = soup.find_all('table', {'id': True})[:5]
        
        for i, table in enumerate(tables):
            print(f"\n📊 表格 {i+1}: {table.get('id')}")
            
            analysis = self.ai_analyzer.analyze_table_structure(table)
            
            print(f"  结构分析:")
            print(f"    列数: {analysis.column_count}")
            print(f"    行数: {analysis.row_count}")
            print(f"    置信度: {analysis.confidence_score:.2f}")
            
            print(f"  检测到的模式:")
            patterns = analysis.detected_patterns
            print(f"    价格列: {patterns.get('price_columns', [])}")
            print(f"    名称列: {patterns.get('name_columns', [])}")
            print(f"    规格列: {patterns.get('spec_columns', [])}")
            
            print(f"  列类型:")
            for col_idx, col_type in patterns.get('column_types', {}).items():
                header = analysis.header_row[col_idx] if col_idx < len(analysis.header_row) else f"列{col_idx}"
                print(f"    {header}: {col_type}")
    
    def demo_compatibility_handling(self):
        """演示兼容性处理"""
        print("\n🔧 兼容性处理演示")
        print("=" * 60)
        
        # 模拟不同的HTML结构
        test_scenarios = [
            {
                'name': '标准4列表格',
                'description': '实例名、vCore、内存、价格',
                'expected_confidence': 0.8
            },
            {
                'name': '简单2列表格', 
                'description': '项目、价格',
                'expected_confidence': 0.7
            },
            {
                'name': '复杂多列表格',
                'description': '包含多种规格信息',
                'expected_confidence': 0.6
            }
        ]
        
        print("  支持的表格类型:")
        for scenario in test_scenarios:
            print(f"    ✓ {scenario['name']}: {scenario['description']}")
            print(f"      预期置信度: {scenario['expected_confidence']}")
    
    def demo_extensibility(self):
        """演示可扩展性"""
        print("\n🚀 可扩展性演示")
        print("=" * 60)
        
        print("  1. 新产品支持:")
        print("     ✓ 无需修改代码，只需添加schema配置")
        print("     ✓ 支持自动schema生成")
        print("     ✓ AI辅助结构识别")
        
        print("\n  2. 新HTML结构支持:")
        print("     ✓ 自适应列类型检测")
        print("     ✓ 灵活的价格模式匹配")
        print("     ✓ 智能去重处理")
        
        print("\n  3. 多策略提取:")
        print("     ✓ Schema优先策略")
        print("     ✓ 自适应优先策略") 
        print("     ✓ 混合策略（推荐）")
        
        print("\n  4. 质量保证:")
        print("     ✓ 置信度评估")
        print("     ✓ 数据验证")
        print("     ✓ 错误处理和回退")
    
    def demo_future_scenarios(self):
        """演示未来场景处理"""
        print("\n🔮 未来场景演示")
        print("=" * 60)
        
        future_scenarios = [
            {
                'scenario': '新的Azure产品页面',
                'solution': '自适应提取 + 自动schema生成',
                'confidence': '高'
            },
            {
                'scenario': '页面结构变化',
                'solution': 'AI重新分析 + schema更新',
                'confidence': '中'
            },
            {
                'scenario': '多语言页面',
                'solution': '模式匹配 + 国际化支持',
                'confidence': '中'
            },
            {
                'scenario': '动态加载内容',
                'solution': 'JavaScript渲染 + 增强解析',
                'confidence': '低（需要额外开发）'
            }
        ]
        
        print("  未来挑战和解决方案:")
        for scenario in future_scenarios:
            print(f"    📋 {scenario['scenario']}")
            print(f"       解决方案: {scenario['solution']}")
            print(f"       可行性: {scenario['confidence']}")
            print()
    
    def generate_architecture_report(self):
        """生成架构报告"""
        print("\n📊 生成架构分析报告")
        print("=" * 60)
        
        report = {
            'architecture_analysis': {
                'current_approach': {
                    'strengths': [
                        '配置驱动的schema管理',
                        'AI增强的自适应解析',
                        '多策略提取框架',
                        '智能去重和验证'
                    ],
                    'limitations': [
                        '需要YAML配置文件维护',
                        'AI分析依赖启发式规则',
                        '复杂页面可能需要人工调优'
                    ]
                },
                'scalability': {
                    'new_products': '高 - 自动schema生成',
                    'structure_changes': '中 - AI重新分析',
                    'volume_scaling': '高 - 并行处理支持'
                },
                'maintainability': {
                    'code_complexity': '中 - 模块化设计',
                    'configuration_management': '简单 - YAML配置',
                    'debugging': '良好 - 详细日志和验证'
                },
                'recommendations': [
                    '建立schema版本管理',
                    '增加更多AI训练数据',
                    '实现配置热更新',
                    '添加性能监控'
                ]
            },
            'generated_time': datetime.now().isoformat()
        }
        
        with open('architecture_analysis_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("  ✓ 架构分析报告已生成: architecture_analysis_report.json")
        
        return report

def main():
    """主演示程序"""
    print("🏛️ Azure价格考古学家 - 灵活架构演示")
    print("=" * 80)
    
    demo = FlexibleArchitectureDemo()
    
    # 1. Schema演进演示
    demo.demo_schema_evolution()
    
    # 2. AI分析演示
    demo.demo_ai_analysis()
    
    # 3. 兼容性处理演示
    demo.demo_compatibility_handling()
    
    # 4. 可扩展性演示
    demo.demo_extensibility()
    
    # 5. 未来场景演示
    demo.demo_future_scenarios()
    
    # 6. 生成架构报告
    report = demo.generate_architecture_report()
    
    print(f"\n🎯 总结:")
    print(f"  当前架构优势: {len(report['architecture_analysis']['current_approach']['strengths'])}项")
    print(f"  可扩展性评级: 高")
    print(f"  维护性评级: 良好")
    print(f"  推荐改进: {len(report['architecture_analysis']['recommendations'])}项")

if __name__ == "__main__":
    main()
