<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure,  Azure Database for MySQL， 价格详情, 定价, 计费" name="keywords"/>
  <meta content="Azure Database for MySQL 服务定价层和详细信息。" name="description"/>
  <title>
   Azure Database for MySQL定价 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/mysql/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px;
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px;
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service {
            position: absolute;
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <style type="text/css">
        .pricing-detail-tab .tab-nav {
                                padding-left: 0 !important;
                                margin-top: 5px;
                                margin-bottom: 0;
                                overflow: hidden;
                            }

                            .pricing-detail-tab .tab-nav li {
                                list-style: none;
                                float: left;
                            }

                            .pricing-detail-tab .tab-nav li.active a {
                                border-bottom: 4px solid #00a3d9;
                            }

                            .pricing-detail-tab .tab-nav li.active a:hover {
                                border-bottom: 4px solid #00a3d9;
                            }

                            .pricing-detail-tab .tab-content .tab-panel {
                                display: none;
                            }

                            .pricing-detail-tab .tab-content .tab-panel.show-md {
                                display: block;
                            }

                            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                                padding-left: 5px;
                                padding-right: 5px;
                                color: #00a3d9;
                                background-color: #FFF;
                            }

                            .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                                color: #FFF;
                                background-color: #00a3d9;
                            }

                            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                                color: #FFF;
                                background-color: #00a3d9;
                            }

                            .pure-content .technical-azure-selector p a,
                            .pure-content .technical-azure-selector table a {
                                background: 0 0;
                                padding: 0;
                                margin: 0 6px;
                                height: 21px;
                                line-height: 22px;
                                font-size: 14px;
                                color: #00a3d9;
                                float: none;
                                display: inline;
                            }
       </style>
       <div class="hide-info" style="display:none;">
        <div class="bg-box">
         <div class="cover-bg">
         </div>
        </div>
        <div class="msg-box">
         <div class="pricing-unavailable-message">
          所选区域不可用
         </div>
        </div>
       </div>
       <tags ms.date="09/30/2015" ms.service="mysql" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/mysql-database%20on%20azure.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/<EMAIL>"/>
          <h2>
           Azure
           <small>
            Database for MySQL
           </small>
          </h2>
          <h4>
           面向应用开发人员的托管 MySQL 数据库服务
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         Azure Database for MySQL 通过内置功能（包括高可用性）提供用于应用开发和部署的完全托管数据库服务，无需额外付费。
        </p>
       </div>
       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Azure Database for MySQL
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent0" href="javascript:void(0)" id="Azure Database for MySQL">
                Azure
                                                            Database for MySQL
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
             <option data-href="#tabContent0" selected="selected" value="Azure Database for MySQL">
              Azure Database for MySQL
             </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国北部 3
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
                <li class="active">
                  <a data-href="#east-china3" href="javascript:void(0)" id="east-china3">
                   中国东部3
                  </a>
                 </li>
              <li class="active">
               <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                中国北部3
               </a>
              </li>
              <li>
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                <option data-href="#east-china3" selected="selected" value="east-china3">
                   中国东部 3
               </option>
             <option data-href="#north-china3" selected="selected" value="north-china3">
              中国北部 3
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#east-china2" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <div class="tab-content">
         <div class="tab-panel" id="tabContent0">
          <!-- BEGIN: TAB-CONTROL -->
          <div class="technical-azure-selector pricing-detail-tab">
           <ul class="tab-nav">
            <li class="active">
             <a data-href="#tabContent6" data-toggle="tab" href="javascript:void(0)" id="home_mysql-basic">
              灵活服务器
             </a>
            </li>
           </ul>
           <!-- BEGIN: TAB-CONTAINER-1 -->
           <div class="tab-content">
            <!-- BEGIN: Level 1 tab content panel 1 -->
            <div class="tab-panel" id="tabContent6">
             <p>
              通过简化的开发人员体验，灵活服务器为数据库提供最大程度的控制，最适合需要满足以下要求的工作负载:
             </p>
             <ul>
              <li>
               适用于数据库优化的自定义维护时段和其他配置参数
              </li>
              <li>
               最高可达 99.99% SLA 的区域冗余和相同区域高可用性
              </li>
              <li>
               适用于成本优化的停止/启动功能和可突发 SKU
              </li>
              <li>
               使用数据传入复制进行混合数据同步
              </li>
              <li>
               脱机和联机迁移支持
              </li>
             </ul>
             <a href="https://docs.microsoft.com/zh-cn/azure/mysql/flexible-server/overview" style="float: none;margin: 0; padding: 0;display: inline;background-color: transparent;color: #006fc3;">
              详细了解
                                                    Azure Database for MySQL 灵活服务器
             </a>
             <div class="technical-azure-selector pricing-detail-tab">
              <ul class="tab-nav">
               <li class="active">
                <a data-href="#tabContent7" data-toggle="tab" href="javascript:void(0)" id="home_postgresql-basic">
                 可突增
                </a>
               </li>
               <li>
                <a data-href="#tabContent8" data-toggle="tab" href="javascript:void(0)" id="home_postgresql-general">
                 常规用途
                </a>
               </li>
               <li>
                <a data-href="#tabContent9" data-toggle="tab" href="javascript:void(0)" id="home_postgresql-memory">
                 业务关键
                </a>
               </li>
              </ul>
              <!-- BEGIN: TAB-CONTAINER-1 -->
              <div class="tab-content">
               <!-- BEGIN: Level 1 tab content panel 1 -->
               <div class="tab-panel" id="tabContent7">
                <p>
                 具有灵活计算要求的工作负载。
                </p>
                <h2>
                 可突发计算
                </h2>
                <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL5" width="100%">
                 <tr>
                  <th align="left">
                   <strong>
                    实例
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    vCore 数
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    内存
                   </strong>
                  </th>
                  <th align="left">
                   <strong>
                    现用现付
                   </strong>
                  </th>
                 </tr>
                 <tr>
                  <td>
                   基本
                  </td>
                  <td>
                   1
                  </td>
                  <td>
                   1 GB
                  </td>
                  <td>
                   ￥ 0.0723/小时
                  </td>
                 </tr>
                 <tr>
                  <td>
                   B1MS
                  </td>
                  <td>
                   1
                  </td>
                  <td>
                   2 GB
                  </td>
                  <td>
                   ￥ 0.1449/小时
                  </td>
                 </tr>
                 <tr>
                  <td>
                   B2S
                  </td>
                  <td>
                   2
                  </td>
                  <td>
                   4 GB
                  </td>
                  <td>
                   ￥ 0.5796/小时
                  </td>
                 </tr>
                 <tr>
                    <td>
                     B2ms
                    </td>
                    <td>
                     2
                    </td>
                    <td>
                     8 GB
                    </td>
                    <td>
                     ￥ 1.1575/小时
                    </td>
                 </tr>
                 <tr>
                    <td>
                     B4ms
                    </td>
                    <td>
                     4
                    </td>
                    <td>
                     16 GB
                    </td>
                    <td>
                     ￥ 2.315/小时
                    </td>
                 </tr>
                 <tr>
                    <td>
                     B8ms
                    </td>
                    <td>
                     8
                    </td>
                    <td>
                     32 GB
                    </td>
                    <td>
                     ￥ 4.6364/小时
                    </td>
                 </tr>
                 <tr>
                    <td>
                     B12ms
                    </td>
                    <td>
                     12
                    </td>
                    <td>
                     48 GB
                    </td>
                    <td>
                     ￥ 6.9515/小时
                    </td>
                 </tr>
                 <tr>
                    <td>
                     B16ms
                    </td>
                    <td>
                     16
                    </td>
                    <td>
                     64 GB
                    </td>
                    <td>
                     ￥ 9.2729/小时
                    </td>
                 </tr>
                 <tr>
                    <td>
                     B20ms
                    </td>
                    <td>
                     20
                    </td>
                    <td>
                     80 GB
                    </td>
                    <td>
                     ￥ 11.58792/小时
                    </td>
                 </tr>
                </table>
                <br/>
                <div class="scroll-table" style="display: block;">
                 <h2>
                  其他 IOPS
                 </h2>
                 <p>
                  可缩放 IOPS 来优化 IO 密集型操作的性能。如果超过内附的 IOPS (3
                                                                    IOPS/GB)，我们将按每月每 IOPS ￥0.3528 的价格向你收费。超额 IOPS
                                                                    按每分钟计费；不足一分钟的，按一分钟计算。请详细了解
                  <a href="https://docs.azure.cn/zh-cn/mysql/concepts-pricing-tiers">
                   超额
                                                                        IOPS
                  </a>
                  。
                 </p>
                 <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL1" width="100%">
                  <thead>
                   <tr>
                    <th align="left">
                     <strong>
                     </strong>
                    </th>
                    <th align="left">
                     <strong>
                      价格
                     </strong>
                    </th>
                   </tr>
                  </thead>
                  <tbody>
                   <tr>
                    <td>
                     IOPS/月
                    </td>
                    <td>
                     ￥ 0.3528
                    </td>
                   </tr>
                  </tbody>
                 </table>
                 <!-- <table cellpadding="0" cellspacing="0" width="100%"
                                                                    id="Azure_Database_For_MySQL15">
                                                                    <thead>
                                                                        <tr>
                                                                            <th align="left"><strong></strong></th>
                                                                            <th align="left"><strong>价格</strong></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        <tr>
                                                                            <td>IOPS/月</td>
                                                                            <td>￥ 0.3528</td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table> -->
                 <br/>
                </div>
                <h2>
                 存储
                </h2>
                <p>
                 需要为对服务器预配的存储空间付费。存储空间最多可预配到 16 TB，每 GB 存储的 IOPS 为 3
                </p>
                <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL19" width="100%">
                 <thead>
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                  </tr>
                 </thead>
                 <tbody>
                  <tr>
                   <td>
                    GB/月
                   </td>
                   <td>
                    ￥ 0.8100
                   </td>
                  </tr>
                 </tbody>
                </table>
                <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL20" width="100%">
                 <thead>
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                  </tr>
                 </thead>
                 <tbody>
                  <tr>
                   <td>
                    GB/月
                   </td>
                   <td>
                    ￥ 0.7653
                   </td>
                  </tr>
                 </tbody>
                </table>
                <br/>
                <!-- <h2>超额 IOPS（预览版）</h2>
                                                <p>可缩放 IOPS 来优化 IO 密集型操作的性能。如果超过内附的 IOPS (3 IOPS/GB)，我们将按每月每 IOPS 0.3528 的价格向你收费。超额 IOPS 按每分钟计费；不足一分钟的，按一分钟计算。请详细了解超额 <a href="#">IOPS</a>。</p>
                                                <table cellpadding="0" cellspacing="0" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th align="left"><strong></strong></th>
                                                            <th align="left"><strong>价格</strong></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                            <tr>
                                                                <td>IOPS/月</td>
                                                                <td>￥ 0.3528</td>
                                                            </tr>
                                                    </tbody>
                                                </table><br> -->
                <h2>
                 备份
                </h2>
                <p>
                 备份存储是与服务器的自动备份关联的存储。增长备份保留期会使服务器使用的备份存储空间增大。如果备份存储空间未超过
                                                                100% 的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GB/月收费。
                </p>
                <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL21" width="100%">
                 <thead>
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     备注
                    </strong>
                   </th>
                  </tr>
                 </thead>
                 <tbody>
                  <tr>
                   <td>
                    GB/月
                   </td>
                   <td>
                    ￥ 0.5472
                   </td>
                   <td style="width: 45%;">
                    由于 GRS 会创建一个副本，因此需要两倍的存储容量。如果您将备份存储配置为异地冗余存储 (GRS)，价格将是此价格的两倍。每 GB 的价格保持不变。
                   </td>
                  </tr>
                 </tbody>
                </table>

                <h2>付费 IO</h2>
                <p>Azure Database for MySQL 将根据工作负载自动缩放 IOPS，而无需手动预配 IOPS。这是一种经济高效的 IO 模型，仅对工作负载消耗的内容收费。</p>

                <!-- 付费IO 只在北部3展示 -->
                <div class="scroll-table" style="display: block;">
                   
                    <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL_IOPS" width="100%">
                        <thead>
                         <tr>
                          <th align="left">
                           <strong>
                           </strong>
                          </th>
                          <th align="left">
                           <strong>
                            价格
                           </strong>
                          </th>
                         </tr>
                        </thead>
                        <tbody>
                         <tr>
                          <td>
                            付费 IO 本地冗余存储(LRS)
                          </td>
                          <td>
                            每百万 IOPS ￥2
                          </td>
                         </tr>
                         <tr>
                            <td>
                                付费 IO 区域冗余存储(ZRS)
                            </td>
                            <td>
                                每百万 IOPS ￥2.04
                            </td>
                         </tr>
                        </tbody>
                    </table>
                </div>
                <!-- 付费IO 只在东部3展示 -->
                <div class="scroll-table" style="display: block;">
                    <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL_IOPS_East3" width="100%">
                        <thead>
                         <tr>
                          <th align="left">
                           <strong>
                           </strong>
                          </th>
                          <th align="left">
                           <strong>
                            价格
                           </strong>
                          </th>
                         </tr>
                        </thead>
                        <tbody>
                         <tr>
                          <td>
                            付费 IO 本地冗余存储(LRS)
                          </td>
                          <td>
                            每百万 IOPS ￥0.35
                          </td>
                         </tr>
     <!--                     <tr>
                            <td>
                                付费 IO 区域冗余存储(ZRS)
                            </td>
                            <td>
                                每百万 IOPS ￥2.5
                            </td>
                         </tr> -->
                        </tbody>
                    </table>
                </div>
                
               </div>
               <!-- END: TAB-CONTAINER-1 -->
               <!-- BEGIN: TAB-CONTAINER-2 -->
               <div class="tab-panel" id="tabContent8">
                <p>
                 大多数业务工作负荷，此类工作负荷需要均衡计算和内存以及可缩放 I/O 吞吐量。
                </p>
                <h2>
                 D 系列
                </h2>
                <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL6" width="100%">
                 <thead>
                  <tr>
                   <th align="left">
                    <strong>
                     实例
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     vCore 数
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     内存
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     现用现付
                    </strong>
                   </th>
                  </tr>
                 </thead>
                 <tbody>
                  <tr>
                   <td>
                    D2ds v4
                   </td>
                   <td>
                    2
                   </td>
                   <td>
                    8 GB
                   </td>
                   <td>
                    ￥ 1.1220/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    D4ds v4
                   </td>
                   <td>
                    4
                   </td>
                   <td>
                    16 GB
                   </td>
                   <td>
                    ￥ 2.2380/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    D8ds v4
                   </td>
                   <td>
                    8
                   </td>
                   <td>
                    32 GB
                   </td>
                   <td>
                    ￥ 4.4820/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    D16ds v4
                   </td>
                   <td>
                    16
                   </td>
                   <td>
                    64 GB
                   </td>
                   <td>
                    ￥ 8.9640/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    D32ds v4
                   </td>
                   <td>
                    32
                   </td>
                   <td>
                    128 GB
                   </td>
                   <td>
                    ￥ 17.9220/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    D48ds v4
                   </td>
                   <td>
                    48
                   </td>
                   <td>
                    192 GB
                   </td>
                   <td>
                    ￥ 26.8860/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    D64ds v4
                   </td>
                   <td>
                    64
                   </td>
                   <td>
                    256 GB
                   </td>
                   <td>
                    ￥ 35.8440/小时
                   </td>
                  </tr>
                 </tbody>
                </table>
                <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL7" width="100%">
                 <thead>
                  <tr>
                   <th align="left">
                    <strong>
                     实例
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     vCore 数
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     内存
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     现用现付
                    </strong>
                   </th>
                  </tr>
                 </thead>
                 <tbody>
                  <tr>
                   <td>
                    D2ds v4
                   </td>
                   <td>
                    2
                   </td>
                   <td>
                    8 GB
                   </td>
                   <td>
                    ￥ 1.06/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    D4ds v4
                   </td>
                   <td>
                    4
                   </td>
                   <td>
                    16 GB
                   </td>
                   <td>
                    ￥ 2.21/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    D8ds v4
                   </td>
                   <td>
                    8
                   </td>
                   <td>
                    32 GB
                   </td>
                   <td>
                    ￥ 4.23/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    D16ds v4
                   </td>
                   <td>
                    16
                   </td>
                   <td>
                    64 GB
                   </td>
                   <td>
                    ￥ 8.47/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    D32ds v4
                   </td>
                   <td>
                    32
                   </td>
                   <td>
                    128 GB
                   </td>
                   <td>
                    ￥ 16.96/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    D48ds v4
                   </td>
                   <td>
                    48
                   </td>
                   <td>
                    192 GB
                   </td>
                   <td>
                    ￥ 39.94/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    D64ds v4
                   </td>
                   <td>
                    64
                   </td>
                   <td>
                    256 GB
                   </td>
                   <td>
                    ￥ 53.12/小时
                   </td>
                  </tr>
                 </tbody>
                </table>
                <br/>
                <h2>
                 存储
                </h2>
                <p>
                 需要为对服务器预配的存储空间付费。存储空间最多可预配到 16 TB，每 GB 存储的 IOPS 为 3
                </p>
                <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL17" width="100%">
                 <thead>
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                  </tr>
                 </thead>
                 <tbody>
                  <tr>
                   <td>
                    GB/月
                   </td>
                   <td>
                    ￥ 0.8100
                   </td>
                  </tr>
                 </tbody>
                </table>
                <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL18" width="100%">
                 <thead>
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                  </tr>
                 </thead>
                 <tbody>
                  <tr>
                   <td>
                    GB/月
                   </td>
                   <td>
                    ￥ 0.7653
                   </td>
                  </tr>
                 </tbody>
                </table>
                <br/>
                <!-- <h2>超额 IOPS（预览版）</h2>
                                                <p>可缩放 IOPS 来优化 IO 密集型操作的性能。如果超过内附的 IOPS (3 IOPS/GB)，我们将按每月每 IOPS 0.3528 的价格向你收费。超额 IOPS 按每分钟计费；不足一分钟的，按一分钟计算。请详细了解超额 <a href="#">IOPS</a>。</p>
                                                <table cellpadding="0" cellspacing="0" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th align="left"><strong></strong></th>
                                                            <th align="left"><strong>价格</strong></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                            <tr>
                                                                <td>IOPS/月</td>
                                                                <td>￥ 0.3528</td>
                                                            </tr>
                                                    </tbody>
                                                </table><br> -->
                <div class="scroll-table" style="display: block;">
                 <h2>
                  其他 IOPS
                 </h2>
                 <p>
                  可缩放 IOPS 来优化 IO 密集型操作的性能。如果超过内附的 IOPS (3
                                                                    IOPS/GB)，我们将按每月每 IOPS ￥0.3528 的价格向你收费。超额 IOPS
                                                                    按每分钟计费；不足一分钟的，按一分钟计算。请详细了解
                  <a href="https://docs.azure.cn/zh-cn/mysql/concepts-pricing-tiers">
                   超额
                                                                        IOPS
                  </a>
                  。
                 </p>
                 <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL2" width="100%">
                  <thead>
                   <tr>
                    <th align="left">
                     <strong>
                     </strong>
                    </th>
                    <th align="left">
                     <strong>
                      价格
                     </strong>
                    </th>
                   </tr>
                  </thead>
                  <tbody>
                   <tr>
                    <td>
                     IOPS/月
                    </td>
                    <td>
                     ￥ 0.3528
                    </td>
                   </tr>
                  </tbody>
                 </table>
                 <!-- <table id="Azure_Database_For_MySQL16" cellpadding="0" cellspacing="0" width="100%">
                                                                    <thead>
                                                                        <tr>
                                                                            <th align="left"><strong></strong></th>
                                                                            <th align="left"><strong>价格</strong></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        <tr>
                                                                            <td>IOPS/月</td>
                                                                            <td>￥ 0.3528</td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table> -->
                 <br/>
                </div>
                <h2>
                 备份
                </h2>
                <p>
                 备份存储是与服务器的自动备份关联的存储。增长备份保留期会使服务器使用的备份存储空间增大。如果备份存储空间未超过
                                                                100% 的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GB/月收费。
                </p>
                <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL22" width="100%">
                 <thead>
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     备注
                    </strong>
                   </th>
                  </tr>
                 </thead>
                 <tbody>
                  <tr>
                   <td>
                    GB/月
                   </td>
                   <td>
                    ￥ 0.5472
                   </td>
                   <td style="width: 45%;">
                    由于 GRS 会创建一个副本，因此需要两倍的存储容量。如果您将备份存储配置为异地冗余存储 (GRS)，价格将是此价格的两倍。每 GB 的价格保持不变。
                   </td>
                  </tr>
                 </tbody>
                </table>
                <h2>
                 只读副本
                </h2>
                <p>
                 有了只读副本，执行大量读取操作的工作负载就能超越单个 Azure Database for MySQL
                                                                灵活服务器的容量向外扩展。每个只读副本按照预配计算资源的 vCore 数量以及每月 GB 存储量计费。
                </p>
               </div>
               <!-- END: TAB-CONTAINER-2 -->
               <!-- BEGIN: TAB-CONTAINER-3 -->
               <div class="tab-panel" id="tabContent9">
                <p>
                 高性能数据库工作负荷，此类工作负荷需要内存中性能来实现更快的事务处理速度和更高的并发。
                </p>
                <h2>
                 E 系列
                </h2>
                <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL4" width="100%">
                 <thead>
                  <tr>
                   <th align="left">
                    <strong>
                     实例
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     vCore 数
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     内存
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     现用现付
                    </strong>
                   </th>
                  </tr>
                 </thead>
                 <tbody>
                  <tr>
                   <td>
                    E2ds v4
                   </td>
                   <td>
                    2
                   </td>
                   <td>
                    16 GB
                   </td>
                   <td>
                    ￥ 1.7580/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E4ds v4
                   </td>
                   <td>
                    4
                   </td>
                   <td>
                    32 GB
                   </td>
                   <td>
                    ￥ 3.5160/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E8ds v4
                   </td>
                   <td>
                    8
                   </td>
                   <td>
                    64 GB
                   </td>
                   <td>
                    ￥ 7.0320/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E16ds v4
                   </td>
                   <td>
                    16
                   </td>
                   <td>
                    128 GB
                   </td>
                   <td>
                    ￥ 14.0580/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E32ds v4
                   </td>
                   <td>
                    32
                   </td>
                   <td>
                    256 GB
                   </td>
                   <td>
                    ￥ 28.1220/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E48ds v4
                   </td>
                   <td>
                    48
                   </td>
                   <td>
                    384 GB
                   </td>
                   <td>
                    ￥ 42.1800/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E64ds v4
                   </td>
                   <td>
                    64
                   </td>
                   <td>
                    504 GB
                   </td>
                   <td>
                    ￥ 56.2440/小时
                   </td>
                  </tr>
                 </tbody>
                </table>
                <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL8" width="100%">
                 <thead>
                  <tr>
                   <th align="left">
                    <strong>
                     实例
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     vCore 数
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     内存
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     现用现付
                    </strong>
                   </th>
                  </tr>
                 </thead>
                 <tbody>
                  <tr>
                   <td>
                    E2ds v4
                   </td>
                   <td>
                    2
                   </td>
                   <td>
                    16 GB
                   </td>
                   <td>
                    ￥ 1.66/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E4ds v4
                   </td>
                   <td>
                    4
                   </td>
                   <td>
                    32 GB
                   </td>
                   <td>
                    ￥ 3.33/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E8ds v4
                   </td>
                   <td>
                    8
                   </td>
                   <td>
                    64 GB
                   </td>
                   <td>
                    ￥ 6.66/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E16ds v4
                   </td>
                   <td>
                    16
                   </td>
                   <td>
                    128 GB
                   </td>
                   <td>
                    ￥ 13.31/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E32ds v4
                   </td>
                   <td>
                    32
                   </td>
                   <td>
                    256 GB
                   </td>
                   <td>
                    ￥ 26.56/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E48ds v4
                   </td>
                   <td>
                    48
                   </td>
                   <td>
                    384 GB
                   </td>
                   <td>
                    ￥ 39.94/小时
                   </td>
                  </tr>
                  <tr>
                   <td>
                    E64ds v4
                   </td>
                   <td>
                    64
                   </td>
                   <td>
                    504 GB
                   </td>
                   <td>
                    ￥ 53.12/小时
                   </td>
                  </tr>
                 </tbody>
                </table>
                <br/>
                <h2>
                 存储
                </h2>
                <p>
                 需要为对服务器预配的存储空间付费。存储空间最多可预配到 16 TB，每 GB 存储的 IOPS 为 3
                </p>
                <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL9" width="100%">
                 <thead>
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                  </tr>
                 </thead>
                 <tbody>
                  <tr>
                   <td>
                    GB/月
                   </td>
                   <td>
                    ￥ 0.8100
                   </td>
                  </tr>
                 </tbody>
                </table>
                <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL10" width="100%">
                 <thead>
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                  </tr>
                 </thead>
                 <tbody>
                  <tr>
                   <td>
                    GB/月
                   </td>
                   <td>
                    ￥ 0.7653
                   </td>
                  </tr>
                 </tbody>
                </table>
                <br/>
                <!-- <h2>超额 IOPS（预览版）</h2>
                                                <p>可缩放 IOPS 来优化 IO 密集型操作的性能。如果超过内附的 IOPS (3 IOPS/GB)，我们将按每月每 IOPS 0.3528 的价格向你收费。超额 IOPS 按每分钟计费；不足一分钟的，按一分钟计算。请详细了解超额 <a href="#">IOPS</a>。</p>
                                                <table cellpadding="0" cellspacing="0" width="100%">
                                                    <thead>
                                                        <tr>
                                                            <th align="left"><strong></strong></th>
                                                            <th align="left"><strong>价格</strong></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                            <tr>
                                                                <td>IOPS/月</td>
                                                                <td>￥ 0.3528</td>
                                                            </tr>
                                                    </tbody>
                                                </table><br> -->
                <div class="scroll-table" style="display: block;">
                 <h2>
                  其他 IOPS
                 </h2>
                 <p>
                  可缩放 IOPS 来优化 IO 密集型操作的性能。如果超过内附的 IOPS (3
                                                                    IOPS/GB)，我们将按每月每 IOPS ￥0.3528 的价格向你收费。超额 IOPS
                                                                    按每分钟计费；不足一分钟的，按一分钟计算。请详细了解
                  <a href="https://docs.azure.cn/zh-cn/mysql/concepts-pricing-tiers">
                   超额
                                                                        IOPS
                  </a>
                  。
                 </p>
                 <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL3" width="100%">
                  <thead>
                   <tr>
                    <th align="left">
                     <strong>
                     </strong>
                    </th>
                    <th align="left">
                     <strong>
                      价格
                     </strong>
                    </th>
                   </tr>
                  </thead>
                  <tbody>
                   <tr>
                    <td>
                     IOPS/月
                    </td>
                    <td>
                     ￥ 0.3528
                    </td>
                   </tr>
                  </tbody>
                 </table>
                 <!-- <table id="Azure_Database_For_MySQL12" cellpadding="0"
                                                                    cellspacing="0" width="100%">
                                                                    <thead>
                                                                        <tr>
                                                                            <th align="left"><strong></strong></th>
                                                                            <th align="left"><strong>价格</strong></th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        <tr>
                                                                            <td>IOPS/月</td>
                                                                            <td>￥ 0.3528</td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table> -->
                 <br/>
                </div>
                <h2>
                 备份
                </h2>
                <p>
                 备份存储是与服务器的自动备份关联的存储。增长备份保留期会使服务器使用的备份存储空间增大。如果备份存储空间未超过
                                                                100% 的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GB/月收费。
                </p>
                <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL13" width="100%">
                 <thead>
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     备注
                    </strong>
                   </th>
                  </tr>
                 </thead>
                 <tbody>
                  <tr>
                   <td>
                    GB/月
                   </td>
                   <td>
                    ￥ 0.5472
                   </td>
                   <td style="width: 45%;">
                    由于 GRS 会创建一个副本，因此需要两倍的存储容量。如果您将备份存储配置为异地冗余存储 (GRS)，价格将是此价格的两倍。每 GB 的价格保持不变。
                   </td>
                  </tr>
                 </tbody>
                </table>
                <table cellpadding="0" cellspacing="0" id="Azure_Database_For_MySQL14" width="100%">
                 <thead>
                  <tr>
                   <th align="left">
                    <strong>
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     价格
                    </strong>
                   </th>
                   <th align="left">
                    <strong>
                     备注
                    </strong>
                   </th>
                  </tr>
                 </thead>
                 <tbody>
                  <tr>
                   <td>
                    GB/月
                   </td>
                   <td>
                    ￥ 0.5472
                   </td>
                   <td style="width: 45%;">
                    由于 GRS 会创建一个副本，因此需要两倍的存储容量。如果您将备份存储配置为异地冗余存储 (GRS)，价格将是此价格的两倍。每 GB 的价格保持不变。
                   </td>
                  </tr>
                 </tbody>
                </table>
                <h2>
                 只读副本
                </h2>
                <p>
                 有了只读副本，执行大量读取操作的工作负载就能超越单个 Azure Database for MySQL
                                                                灵活服务器的容量向外扩展。每个只读副本按照预配计算资源的 vCore 数量以及每月 GB 存储量计费。
                </p>
               </div>
               <!-- END: TAB-CONTAINER-3 -->
              </div>
             </div>
            </div>
            <!-- END: TAB-CONTAINER-3 -->
           </div>
          </div>
         </div>
        </div>
       </div>
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <h3>
          MySQL Database on Azure 迁移客户常见问题解答
         </h3>
         <p>
          我们列下了很详尽的迁移指南，请参考
          <a href="../../../support/announcement/mysql-migration-faq.html">
           MySQL Database on
                                        Azure 迁移常见问题与指南
          </a>
          。
         </p>
         <h3>
          常规
         </h3>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_mysql_question1">
             基本层、常规用途层和内存优化层之间的区别是什么？
            </a>
            <section>
             <p>
              基本层专为需要轻型计算和 I/O
                                                    性能的工作负荷而设计。相关示例包括用于开发或测试的服务器，或者不常使用的小型应用程序。常规用途层适用于大多数业务工作负荷，此类工作负荷需要均衡计算和内存以及可缩放
                                                    I/O 吞吐量。相关示例包括用于托管 Web
                                                    和移动应用的服务器，以及其他企业应用程序。内存优化层适用于高性能数据库工作负荷，此类工作负荷需要内存中性能来实现更快的事务处理速度和更高的并发。相关示例包括用于处理实时数据的服务器，以及高性能事务性应用或分析应用。请参阅
              <a href="https://docs.azure.cn/zh-cn/mysql/concepts-pricing-tiers/">
               文档
              </a>
              ，了解详细信息。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_mysql_question2">
             MYSQL 是如何计费的？
            </a>
            <section>
             <p>
              MySQL 是按 Server 来计费的，与 SQL Database 相反（SQL Database 是按照数据库来收费的）
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_mysql_question3">
             My SQL Database 在账单中以何种方式计算？
            </a>
            <section>
             <p>
              对于所有层，服务根据定价层、在 vCore 中预配的计算以及为服务器和备份预配的存储空间（GB/月）按可预测的每小时费率计费。vCore
                                                    小时数、服务器的存储空间（GB/月）和备份的存储空间（GB/月）作为单独的行项出现在订单上。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_mysql_question4">
             如果一个 MySQL 服务器处于活动状态的时间少于 1 小时或使用更高定价层的时间少于
                                                1 小时，会怎样计费？
            </a>
            <section>
             <p>
              需要为 MySQL
                                                    存在的每个小时或不足一小时的部分付费，而无需考虑服务器是否在整个小时内处于活动状态。如果已缩放数据库，在这个小时内，按照使用的最高定价层、预配的
                                                    vCore 和存储空间计费。
             </p>
             <p>
              例如：
             </p>
             <ul>
              <li>
               如果创建一个 MySQL 服务器并在 5 分钟后删除它，则按照预配的计算和存储空间收取一整个小时的费用。
              </li>
              <li>
               如果在常规用途层创建一个具有 8 个 vCore 的 MySQL 服务器，并在常规用途层中立即将其升级为 16 个
                                                        vCore，则第一个小时按 16 个 vCore 收费。
              </li>
             </ul>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_mysql_question5">
             如何计算备份费用？
            </a>
            <section>
             <p>
              备份存储是与服务器的自动备份关联的存储。增长备份保留期会使 MySQL 服务器使用的备份存储空间增大。如果备份存储空间未超过 100%
                                                    的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GB/月收费。例如，如果数据库存储空间大小为 100
                                                    GB，则可以获得 100 GB 的备份，无需额外付费。但是，如果备份为 110 GB，则需要为 10 GB 付费。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="mysql-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         我们保证，客户的 Azure Database for MySQL 服务器与我们的 Internet 网关至少可在 99.99%
                                的时间内保持连接。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/mysql/index.html" id="pricing_mysql_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
            var token = '<input name="__RequestVerificationToken" type="hidden" value="dEG1TJI2W5mMj8WKXt85yLgsQNITtN7SiMu3MAc9CSHbrO-Ne_9JZzpgNXK1BENnmpPLozM99p3Ch6p2fgwctzrKMzm9hwC7lKRxU7y710s1" />';
            token = $(token).val();
            return token;
        }
        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
        }

        setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js" type="text/javascript">
  </script>
  <script src=" /Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <!-- <script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script> -->
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
