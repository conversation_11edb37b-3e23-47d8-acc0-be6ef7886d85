base_fields:
- name: name
  required: true
  type: text
- name: vcores
  pattern: (\d+)
  required: false
  type: number
- name: memory_gb
  pattern: ([\d\.]+)\s*GB
  required: false
  type: number
product_name: Data Factory SSIS
table_schemas:
- category_rules:
    Av2: SSIS Av2
    Dv2: SSIS Dv2
    Dv3: SSIS Dv3
    E.*v4: SSIS Ev4
    Ev3: SSIS Ev3
  column_mapping:
    0:
      name: instance_name
      required: true
      type: text
    1:
      name: vcores
      pattern: (\d+)
      type: number
    2:
      name: memory_gb
      pattern: ([\d\.]+)\s*GB
      type: number
    3:
      name: temp_storage
      type: text
    4:
      name: license_price
      required: true
      type: price
    5:
      name: hybrid_benefit
      type: price
  price_patterns:
  - ￥([\d,\.]+)/小时
  - 约￥([\d,\.]+)/月
  service_tier_rules:
    企业: Enterprise
    标准: Standard
  table_id_pattern: data-factory-ssis
  title_selector: h3
