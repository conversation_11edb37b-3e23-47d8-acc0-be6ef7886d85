#!/usr/bin/env python3
"""
Azure中国定价网站考古学家 - 主控制器
从HTML文件中智能提取Azure产品价格数据的通用框架
"""

import os
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import asdict

from universal_azure_price_extractor import CompleteProductAnalysis
from ssis_extractor import SSISExtractor
from mysql_extractor import MySQLExtractor
from cosmos_db_extractor import CosmosDBExtractor
from data_exporter import UniversalDataExporter

class AzurePriceArchaeologist:
    """Azure价格考古学家 - 主控制器"""

    def __init__(self, config_file: str = 'soft-category.json', html_dir: str = 'prod-html'):
        self.config_file = config_file
        self.html_dir = html_dir
        self.exporter = UniversalDataExporter()

        # 注册产品提取器
        self.extractors = {
            'Data Factory SSIS': SSISExtractor(),
            'Azure Database for MySQL': MySQLExtractor(),
            'Azure Cosmos DB': CosmosDBExtractor()
        }

        # HTML文件映射 - 现在从prod-html文件夹读取
        self.html_files = {
            'Data Factory SSIS': 'ssis-index.html',
            'Azure Database for MySQL': 'mysql-index.html',
            'Azure Cosmos DB': 'cosmos-db-index.html'
        }

        self.results = {}
    
    def discover_available_products(self) -> List[str]:
        """发现可用的产品"""
        available_products = []
        for product_name, html_file in self.html_files.items():
            full_path = os.path.join(self.html_dir, html_file)
            if os.path.exists(full_path):
                available_products.append(product_name)
                print(f"✓ 发现产品: {product_name} ({html_file})")
            else:
                print(f"⚠️ 未找到 {product_name} 的HTML文件: {full_path}")

        return available_products
    
    def extract_single_product(self, product_name: str) -> Optional[CompleteProductAnalysis]:
        """提取单个产品的价格数据"""
        html_file = self.html_files.get(product_name)
        if not html_file:
            print(f"❌ 未知产品: {product_name}")
            return None

        full_path = os.path.join(self.html_dir, html_file)
        if not os.path.exists(full_path):
            print(f"❌ 未找到 {product_name} 的HTML文件: {full_path}")
            return None

        try:
            print(f"\n🔍 开始提取 {product_name} 的价格数据...")

            # 读取HTML文件
            with open(full_path, 'r', encoding='utf-8') as f:
                html_content = f.read()

            # 检查是否有对应的提取器
            if product_name in self.extractors:
                # 使用专用提取器
                extractor = self.extractors[product_name]
                analysis = extractor.extract_all_regional_pricing(html_content, self.config_file)

                print(f"✅ {product_name} 数据提取完成 (专用提取器)")
                print(f"   总区域数: {analysis.total_regions}")
                print(f"   完整服务区域: {analysis.full_service_regions}个")
                print(f"   受限服务区域: {analysis.partial_service_regions}个")

                return analysis
            else:
                # 使用AI增强的自适应提取器
                print(f"   使用AI增强提取器处理 {product_name}")
                return self._extract_with_ai_enhanced_extractor(html_content, product_name, full_path)

        except Exception as e:
            print(f"❌ {product_name} 数据提取失败: {e}")
            return None

    def _extract_with_ai_enhanced_extractor(self, html_content: str, product_name: str, file_path: str) -> Optional[CompleteProductAnalysis]:
        """使用AI增强提取器处理未知产品"""
        try:
            from ai_enhanced_extractor import AdaptiveExtractor

            adaptive_extractor = AdaptiveExtractor()
            result = adaptive_extractor.extract_unknown_product(html_content, product_name)

            if not result or result['successfully_extracted'] == 0:
                print(f"   ❌ AI提取器未能提取到有效数据")
                return None

            # 转换为CompleteProductAnalysis格式
            regional_data = []

            # 创建一个虚拟的区域数据（因为AI提取器不处理区域逻辑）
            from universal_azure_price_extractor import RegionalPricingData, PricingTable, ProductInstance, PriceDetail

            # 转换AI提取的表格为标准格式
            converted_tables = []
            total_instances = 0

            for ai_table in result['tables']:
                instances = []
                for ai_instance in ai_table.get('instances', []):
                    # 转换价格格式
                    price_data = ai_instance.get('price', {})
                    if price_data:
                        price = PriceDetail(
                            hourly_price=price_data.get('hourly_price', price_data.get('unit_price', 0)),
                            monthly_price=price_data.get('monthly_price'),
                            currency=price_data.get('currency', 'CNY'),
                            pricing_model=price_data.get('pricing_model', 'auto-detected')
                        )

                        instance = ProductInstance(
                            name=ai_instance.get('name', ''),
                            price=price,
                            specifications=ai_instance.get('attributes', {})
                        )
                        instances.append(instance)
                        total_instances += 1

                if instances:
                    table = PricingTable(
                        table_id=ai_table.get('table_id', ''),
                        title=ai_table.get('title', ''),
                        category=ai_table.get('category', 'auto-detected'),
                        service_tier=ai_table.get('service_tier', 'standard'),
                        instances=instances
                    )
                    converted_tables.append(table)

            # 创建单个区域数据（AI提取器不区分区域）
            regional_data.append(RegionalPricingData(
                region_id='global',
                region_name='全球（AI提取）',
                config_status='✅ AI自动提取',
                excluded_tables=[],
                available_tables=converted_tables,
                total_available_configurations=total_instances,
                service_level='🤖 AI识别服务'
            ))

            # 生成业务洞察
            business_insights = {
                'extraction_method': 'AI Enhanced',
                'confidence_scores': [analysis.get('confidence_score', 0) for analysis in result.get('analysis_results', [])],
                'total_tables_found': result.get('total_tables_found', 0),
                'successfully_extracted': result.get('successfully_extracted', 0),
                'recommendations': [
                    f"AI成功识别 {len(converted_tables)} 个表格",
                    f"建议人工验证提取结果的准确性",
                    f"可考虑为 {product_name} 创建专用schema配置"
                ]
            }

            from universal_azure_price_extractor import CompleteProductAnalysis
            analysis = CompleteProductAnalysis(
                product_name=product_name,
                extraction_timestamp=datetime.now().isoformat(),
                total_regions=1,
                full_service_regions=1,
                partial_service_regions=0,
                regional_data=regional_data,
                business_insights=business_insights
            )

            print(f"✅ {product_name} AI提取完成")
            print(f"   发现表格: {result.get('total_tables_found', 0)}个")
            print(f"   成功提取: {result.get('successfully_extracted', 0)}个")
            print(f"   总实例数: {total_instances}个")

            return analysis

        except Exception as e:
            print(f"❌ AI增强提取失败: {e}")
            return None
    
    def extract_all_products(self) -> Dict[str, CompleteProductAnalysis]:
        """提取所有可用产品的价格数据"""
        available_products = self.discover_available_products()
        
        print(f"🚀 开始Azure价格考古挖掘...")
        print(f"📋 发现 {len(available_products)} 个可用产品: {', '.join(available_products)}")
        
        results = {}
        for product_name in available_products:
            analysis = self.extract_single_product(product_name)
            if analysis:
                results[product_name] = analysis
        
        self.results = results
        return results
    
    def export_all_results(self, output_dir: str = "output"):
        """导出所有结果"""
        if not self.results:
            print("❌ 没有可导出的数据，请先运行extract_all_products()")
            return
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"\n📤 开始导出数据到 {output_dir} 目录...")
        
        # 单产品导出
        for product_name, analysis in self.results.items():
            safe_name = product_name.replace(' ', '_').replace('/', '_')
            
            # Excel导出
            excel_file = os.path.join(output_dir, f"{safe_name}_pricing_analysis.xlsx")
            self.exporter.export_to_excel(analysis, excel_file)
            print(f"✅ {product_name} Excel报告: {excel_file}")
            
            # JSON导出
            json_file = os.path.join(output_dir, f"{safe_name}_pricing_data.json")
            self.exporter.export_to_json(analysis, json_file)
            print(f"✅ {product_name} JSON数据: {json_file}")
            
            # Markdown摘要
            summary = self.exporter.generate_summary_report(analysis)
            md_file = os.path.join(output_dir, f"{safe_name}_summary_report.md")
            with open(md_file, 'w', encoding='utf-8') as f:
                f.write(summary)
            print(f"✅ {product_name} 摘要报告: {md_file}")
        
        # 多产品对比
        if len(self.results) > 1:
            comparison_file = os.path.join(output_dir, "azure_products_comparison.xlsx")
            self.exporter.export_comparison_report(self.results, comparison_file)
            print(f"✅ 产品对比报告: {comparison_file}")
        
        # 生成总体摘要
        self._generate_overall_summary(output_dir)
        
        print(f"\n🎉 所有数据导出完成！输出目录: {output_dir}")
    
    def _generate_overall_summary(self, output_dir: str):
        """生成总体摘要报告"""
        summary = f"""
# Azure中国定价网站考古挖掘报告

## 挖掘概况
- 挖掘时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 发现产品数: {len(self.results)}
- 配置文件: {self.config_file}

## 产品发现清单
"""
        
        for product_name, analysis in self.results.items():
            summary += f"""
### {product_name}
- **总区域数**: {analysis.total_regions}
- **完整服务区域**: {analysis.full_service_regions}个
- **受限服务区域**: {analysis.partial_service_regions}个
- **数据提取时间**: {analysis.extraction_timestamp}

#### 区域服务分布
"""
            for region in analysis.regional_data:
                summary += f"- {region.region_name}: {region.service_level} ({len(region.available_tables)}个表格, {region.total_available_configurations}个配置)\n"
        
        summary += f"""
## 跨产品洞察

### 区域服务能力对比
"""
        
        # 分析跨产品的区域服务能力
        all_regions = set()
        for analysis in self.results.values():
            all_regions.update(r.region_id for r in analysis.regional_data)
        
        for region_id in sorted(all_regions):
            region_name = None
            region_services = []
            
            for product_name, analysis in self.results.items():
                region_data = next((r for r in analysis.regional_data if r.region_id == region_id), None)
                if region_data:
                    if not region_name:
                        region_name = region_data.region_name
                    service_level = region_data.service_level.replace('🟢', '').replace('🟡', '').replace('🟠', '').replace('🔴', '').strip()
                    region_services.append(f"{product_name}: {service_level}")
            
            if region_name:
                summary += f"\n#### {region_name}\n"
                for service in region_services:
                    summary += f"- {service}\n"
        
        summary += f"""
## 考古发现总结

### 主要发现
1. **新老区域策略差异**: 新区域(北部3、东部2等)通常提供更完整的服务
2. **产品差异化部署**: 不同产品在同一区域的服务级别可能不同
3. **配置驱动管理**: 通过配置文件实现精细化的区域服务管理

### 业务建议
1. **优先推荐区域**: 选择在多个产品中都提供完整服务的区域
2. **产品组合策略**: 根据区域服务能力规划产品组合部署
3. **成本优化**: 利用区域差异化定价进行成本优化

---
*本报告由Azure价格考古学家自动生成*
"""
        
        summary_file = os.path.join(output_dir, "overall_summary_report.md")
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary)
        print(f"✅ 总体摘要报告: {summary_file}")
    
    def get_product_statistics(self) -> Dict[str, Any]:
        """获取产品统计信息"""
        if not self.results:
            return {}
        
        stats = {
            'total_products': len(self.results),
            'total_regions': 0,
            'total_tables': 0,
            'total_configurations': 0,
            'products': {}
        }
        
        all_regions = set()
        
        for product_name, analysis in self.results.items():
            product_stats = {
                'regions': analysis.total_regions,
                'full_service_regions': analysis.full_service_regions,
                'partial_service_regions': analysis.partial_service_regions,
                'total_tables': sum(len(r.available_tables) for r in analysis.regional_data),
                'total_configurations': sum(r.total_available_configurations for r in analysis.regional_data)
            }
            
            stats['products'][product_name] = product_stats
            stats['total_tables'] += product_stats['total_tables']
            stats['total_configurations'] += product_stats['total_configurations']
            
            all_regions.update(r.region_id for r in analysis.regional_data)
        
        stats['total_regions'] = len(all_regions)
        return stats

def main():
    """主程序"""
    print("🏛️ Azure中国定价网站考古学家")
    print("=" * 50)
    
    # 创建考古学家实例
    archaeologist = AzurePriceArchaeologist()
    
    # 提取所有产品数据
    results = archaeologist.extract_all_products()
    
    if not results:
        print("❌ 没有成功提取任何产品数据")
        return
    
    # 导出所有结果
    archaeologist.export_all_results()
    
    # 显示统计信息
    stats = archaeologist.get_product_statistics()
    print(f"\n📊 考古挖掘统计:")
    print(f"   发现产品: {stats['total_products']}个")
    print(f"   覆盖区域: {stats['total_regions']}个")
    print(f"   挖掘表格: {stats['total_tables']}个")
    print(f"   提取配置: {stats['total_configurations']}个")
    
    print(f"\n🎯 各产品详情:")
    for product_name, product_stats in stats['products'].items():
        print(f"   {product_name}:")
        print(f"     - 区域数: {product_stats['regions']}")
        print(f"     - 表格数: {product_stats['total_tables']}")
        print(f"     - 配置数: {product_stats['total_configurations']}")

if __name__ == "__main__":
    main()
