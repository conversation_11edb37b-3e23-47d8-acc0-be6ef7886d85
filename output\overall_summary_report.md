
# Azure中国定价网站考古挖掘报告

## 挖掘概况
- 挖掘时间: 2025-06-20 15:19:05
- 发现产品数: 2
- 配置文件: soft-category.json

## 产品发现清单

### Data Factory SSIS
- **总区域数**: 5
- **完整服务区域**: 3个
- **受限服务区域**: 2个
- **数据提取时间**: 2025-06-20T15:19:05.124242

#### 区域服务分布
- 中国北部 3: 🟢 完整服务 (10个表格, 38个配置)
- 中国北部: 🟠 基础服务 (2个表格, 2个配置)
- 中国东部: 🟠 基础服务 (2个表格, 2个配置)
- 中国北部 2: 🟢 完整服务 (10个表格, 38个配置)
- 中国东部 2: 🟢 完整服务 (10个表格, 38个配置)

### Azure Database for MySQL
- **总区域数**: 6
- **完整服务区域**: 4个
- **受限服务区域**: 0个
- **数据提取时间**: 2025-06-20T15:19:05.222727

#### 区域服务分布
- 中国北部 3: 🟢 完整服务 (13个表格, 34个配置)
- 中国东部 3: 🟢 完整服务 (13个表格, 33个配置)
- 中国北部: 🔴 无服务 (0个表格, 0个配置)
- 中国东部: 🔴 无服务 (0个表格, 0个配置)
- 中国北部 2: 🟢 完整服务 (12个表格, 32个配置)
- 中国东部 2: 🟢 完整服务 (12个表格, 32个配置)

## 跨产品洞察

### 区域服务能力对比

#### 中国东部
- Data Factory SSIS: 基础服务
- Azure Database for MySQL: 无服务

#### 中国东部 2
- Data Factory SSIS: 完整服务
- Azure Database for MySQL: 完整服务

#### 中国东部 3
- Azure Database for MySQL: 完整服务

#### 中国北部
- Data Factory SSIS: 基础服务
- Azure Database for MySQL: 无服务

#### 中国北部 2
- Data Factory SSIS: 完整服务
- Azure Database for MySQL: 完整服务

#### 中国北部 3
- Data Factory SSIS: 完整服务
- Azure Database for MySQL: 完整服务

## 考古发现总结

### 主要发现
1. **新老区域策略差异**: 新区域(北部3、东部2等)通常提供更完整的服务
2. **产品差异化部署**: 不同产品在同一区域的服务级别可能不同
3. **配置驱动管理**: 通过配置文件实现精细化的区域服务管理

### 业务建议
1. **优先推荐区域**: 选择在多个产品中都提供完整服务的区域
2. **产品组合策略**: 根据区域服务能力规划产品组合部署
3. **成本优化**: 利用区域差异化定价进行成本优化

---
*本报告由Azure价格考古学家自动生成*
