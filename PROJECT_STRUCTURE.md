# Azure中国定价网站考古学家 - 项目结构

## 📁 项目目录结构

```
AzureCNArchaeologist/
├── 📂 prod-html/                    # 生产HTML文件
│   ├── ssis-index.html             # SSIS产品页面
│   ├── mysql-index.html            # MySQL产品页面
│   └── cosmos-db-index.html        # Cosmos DB产品页面
│
├── 📂 schemas/                      # 产品Schema配置
│   ├── ssis_schema.yaml            # SSIS产品Schema
│   └── mysql_schema.yaml           # MySQL产品Schema
│
├── 📂 output/                       # 数据输出目录
│   ├── cosmos_db_*.json/.xlsx/.md  # Cosmos DB提取结果
│   ├── mysql_*.json/.xlsx/.md      # MySQL提取结果
│   └── ssis_*.json/.xlsx/.md       # SSIS提取结果
│
├── 🔧 核心框架文件
│   ├── ai_enhanced_extractor.py    # AI增强自适应提取器
│   ├── flexible_schema_extractor.py # 灵活Schema提取器
│   ├── unified_extraction_framework.py # 统一提取框架
│   └── universal_azure_price_extractor.py # 通用基础提取器
│
├── 🎯 产品特定提取器
│   ├── ssis_extractor.py           # SSIS专用提取器
│   ├── mysql_extractor.py          # MySQL专用提取器
│   └── extract_cosmos_db.py        # Cosmos DB提取器
│
├── 📊 数据处理和导出
│   ├── data_exporter.py            # 通用数据导出器
│   └── azure_price_archaeologist.py # 主控制器
│
├── ⚙️ 配置文件
│   └── soft-category.json          # 区域配置文件
│
└── 📖 文档
    ├── README.md                   # 项目说明
    └── PROJECT_STRUCTURE.md       # 项目结构说明
```

## 🚀 核心组件说明

### 1. AI增强提取器 (`ai_enhanced_extractor.py`)
- **功能**: 自适应HTML解析，无需预定义Schema
- **特点**: 智能表格分析、置信度评估、自动列类型检测
- **适用**: 新产品、未知结构的HTML页面

### 2. 灵活Schema提取器 (`flexible_schema_extractor.py`)
- **功能**: 基于YAML配置的结构化提取
- **特点**: 配置驱动、可扩展、版本化管理
- **适用**: 已知产品、稳定的HTML结构

### 3. 统一提取框架 (`unified_extraction_framework.py`)
- **功能**: 整合多种提取策略的统一入口
- **策略**: Schema优先、自适应优先、混合策略
- **特点**: 自动选择最佳提取方法

### 4. 产品特定提取器
- **SSIS**: 支持虚拟机实例、混合优惠等复杂数据
- **MySQL**: 支持计算实例、存储、IOPS等多种类型
- **Cosmos DB**: 使用AI增强提取器处理复杂定价结构

## 📊 数据流程

```
HTML文件 → 提取器选择 → 数据解析 → 去重处理 → 格式化输出
    ↓           ↓           ↓           ↓           ↓
prod-html/  框架选择    AI/Schema    智能去重    output/
```

## 🔧 使用方法

### 快速开始
```bash
# 提取单个产品 (Cosmos DB)
python extract_cosmos_db.py

# 提取所有产品
python azure_price_archaeologist.py

# 使用AI增强提取器处理新产品
python ai_enhanced_extractor.py
```

### 添加新产品
1. 将HTML文件放入 `prod-html/` 目录
2. 选择提取策略：
   - **已知结构**: 创建Schema配置文件
   - **未知结构**: 使用AI增强提取器
   - **混合方式**: 使用统一框架自动选择

## 📈 扩展性设计

### 支持的扩展场景
- ✅ 新Azure产品页面
- ✅ HTML结构变化
- ✅ 多语言页面支持
- ✅ 大规模批量处理

### 架构优势
- **模块化设计**: 各组件独立，易于维护
- **策略模式**: 多种提取策略可选
- **配置驱动**: 无需修改代码即可支持新产品
- **AI增强**: 自适应处理未知结构

## 🎯 数据质量保证

### 质量控制机制
- **置信度评估**: AI分析提供置信度分数
- **数据验证**: 自动检查数据完整性
- **去重处理**: 智能识别和去除重复数据
- **多格式输出**: JSON、Excel、Markdown多种格式

### 验证建议
1. 检查置信度分数 (>0.7为良好)
2. 验证价格格式一致性
3. 确认表格分类准确性
4. 检查重复数据

## 🔮 未来发展方向

### 短期目标
- [ ] 增加更多Azure产品支持
- [ ] 优化AI分析算法
- [ ] 实现配置热更新

### 长期愿景
- [ ] 实时价格监控
- [ ] 价格趋势分析
- [ ] 成本优化建议
- [ ] Web界面开发

## 📝 维护说明

### 定期维护任务
- 更新产品Schema配置
- 验证提取数据准确性
- 清理临时文件和缓存
- 备份重要配置文件

### 故障排除
- 检查HTML文件路径
- 验证Schema配置语法
- 查看详细错误日志
- 使用AI增强提取器作为备选方案

---
*最后更新: 2025-06-20*
