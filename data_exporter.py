import pandas as pd
from typing import Dict, Any
from universal_azure_price_extractor import CompleteProductAnalysis

class UniversalDataExporter:
    """通用数据导出器"""
    
    @staticmethod
    def export_to_excel(analysis: CompleteProductAnalysis, file_path: str):
        """导出为Excel格式"""
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            
            # 区域概览表
            overview_data = []
            for region in analysis.regional_data:
                overview_data.append({
                    'region_id': region.region_id,
                    'region_name': region.region_name,
                    'config_status': region.config_status,
                    'service_level': region.service_level,
                    'excluded_table_count': len(region.excluded_tables),
                    'available_table_count': len(region.available_tables),
                    'total_configurations': region.total_available_configurations
                })
            
            overview_df = pd.DataFrame(overview_data)
            overview_df.to_excel(writer, sheet_name='区域概览', index=False)
            
            # 为每个区域创建详细的价格表
            for region in analysis.regional_data:
                if not region.available_tables:
                    continue
                    
                sheet_name = region.region_name[:30]  # Excel工作表名称限制
                pricing_data = []
                
                for table in region.available_tables:
                    for instance in table.instances:
                        row = {
                            '区域': region.region_name,
                            '配置状态': region.config_status,
                            '表格标题': table.title,
                            '产品类别': table.category,
                            '服务层级': table.service_tier,
                            '实例名称': instance.name,
                            '小时价格': instance.price.hourly_price,
                            '月度价格': instance.price.monthly_price,
                            '货币': instance.price.currency,
                            '定价模式': instance.price.pricing_model
                        }
                        
                        # 添加规格信息
                        if instance.specifications:
                            for key, value in instance.specifications.items():
                                if value is not None:
                                    row[key] = value
                        
                        pricing_data.append(row)
                
                if pricing_data:
                    pricing_df = pd.DataFrame(pricing_data)
                    pricing_df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # 业务洞察汇总
            insights_data = [
                ['完整服务区域数', analysis.full_service_regions],
                ['受限服务区域数', analysis.partial_service_regions],
                ['总区域数', analysis.total_regions]
            ]

            # 安全地添加策略分析
            if 'strategy_analysis' in analysis.business_insights:
                insights_data.append(['策略分析', analysis.business_insights['strategy_analysis']])

            # 安全地添加建议
            recommendations = analysis.business_insights.get('recommendations', [])
            for i, recommendation in enumerate(recommendations):
                insights_data.append([f'建议{i+1}', recommendation])
            
            insights_df = pd.DataFrame(insights_data, columns=['指标', '值'])
            insights_df.to_excel(writer, sheet_name='业务洞察', index=False)
    
    @staticmethod
    def export_to_json(analysis: CompleteProductAnalysis, file_path: str):
        """导出为JSON格式"""
        import json
        from dataclasses import asdict
        
        # 转换为字典
        data = asdict(analysis)
        
        # 写入JSON文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    @staticmethod
    def generate_summary_report(analysis: CompleteProductAnalysis) -> str:
        """生成摘要报告"""
        report = f"""
# {analysis.product_name} 区域定价分析报告

## 基本信息
- 产品: {analysis.product_name}
- 分析时间: {analysis.extraction_timestamp}
- 总区域数: {analysis.total_regions}
- 完整服务区域: {analysis.full_service_regions}个
- 受限服务区域: {analysis.partial_service_regions}个

## 区域服务级别详情
"""
        
        for region in analysis.regional_data:
            report += f"""
### {region.region_name}
- **服务级别**: {region.service_level}
- **配置状态**: {region.config_status}
- **排除表格**: {len(region.excluded_tables)}个
- **可用表格**: {len(region.available_tables)}个  
- **实例配置**: {region.total_available_configurations}个
"""
            
            if region.available_tables:
                categories = list(set(t.category for t in region.available_tables))
                service_tiers = list(set(t.service_tier for t in region.available_tables))
                report += f"- **支持类别**: {', '.join(categories)}\n"
                report += f"- **服务层级**: {', '.join(service_tiers)}\n"
        
        report += f"""
## 业务建议
"""
        recommendations = analysis.business_insights.get('recommendations', [])
        for recommendation in recommendations:
            report += f"- {recommendation}\n"
        
        report += f"""
## 配置策略分析
{analysis.business_insights.get('strategy_analysis', '基于AI增强提取的数据分析')}

### 完整服务区域
{', '.join(analysis.business_insights.get('full_service_regions', []))}

### 受限服务区域
{', '.join(analysis.business_insights.get('partial_service_regions', []))}
"""
        
        return report
    
    @staticmethod
    def export_comparison_report(analyses: Dict[str, CompleteProductAnalysis], file_path: str):
        """导出多产品对比报告"""
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            
            # 产品对比概览
            comparison_data = []
            for product_name, analysis in analyses.items():
                comparison_data.append({
                    '产品名称': product_name,
                    '总区域数': analysis.total_regions,
                    '完整服务区域数': analysis.full_service_regions,
                    '受限服务区域数': analysis.partial_service_regions,
                    '分析时间': analysis.extraction_timestamp
                })
            
            comparison_df = pd.DataFrame(comparison_data)
            comparison_df.to_excel(writer, sheet_name='产品对比概览', index=False)
            
            # 区域服务级别对比
            region_comparison_data = []
            all_regions = set()
            for analysis in analyses.values():
                all_regions.update(r.region_id for r in analysis.regional_data)
            
            for region_id in sorted(all_regions):
                row = {'区域ID': region_id}
                region_name = None
                
                for product_name, analysis in analyses.items():
                    region_data = next((r for r in analysis.regional_data if r.region_id == region_id), None)
                    if region_data:
                        if not region_name:
                            region_name = region_data.region_name
                        row[f'{product_name}_服务级别'] = region_data.service_level
                        row[f'{product_name}_可用表格数'] = len(region_data.available_tables)
                        row[f'{product_name}_实例配置数'] = region_data.total_available_configurations
                    else:
                        row[f'{product_name}_服务级别'] = '不支持'
                        row[f'{product_name}_可用表格数'] = 0
                        row[f'{product_name}_实例配置数'] = 0
                
                row['区域名称'] = region_name or region_id
                region_comparison_data.append(row)
            
            region_comparison_df = pd.DataFrame(region_comparison_data)
            region_comparison_df.to_excel(writer, sheet_name='区域服务级别对比', index=False)

# 使用示例
def main():
    """数据导出器使用示例"""
    from ssis_extractor import SSISExtractor
    from mysql_extractor import MySQLExtractor
    
    # 提取SSIS数据
    ssis_extractor = SSISExtractor()
    with open('ssis-index.html', 'r', encoding='utf-8') as f:
        ssis_html = f.read()
    ssis_analysis = ssis_extractor.extract_all_regional_pricing(ssis_html, 'soft-category.json')
    
    # 提取MySQL数据
    mysql_extractor = MySQLExtractor()
    with open('mysql-index.html', 'r', encoding='utf-8') as f:
        mysql_html = f.read()
    mysql_analysis = mysql_extractor.extract_all_regional_pricing(mysql_html, 'soft-category.json')
    
    # 导出数据
    exporter = UniversalDataExporter()
    
    # 单产品导出
    exporter.export_to_excel(ssis_analysis, 'ssis_pricing_analysis.xlsx')
    exporter.export_to_excel(mysql_analysis, 'mysql_pricing_analysis.xlsx')
    
    # 生成摘要报告
    ssis_summary = exporter.generate_summary_report(ssis_analysis)
    with open('ssis_summary_report.md', 'w', encoding='utf-8') as f:
        f.write(ssis_summary)
    
    mysql_summary = exporter.generate_summary_report(mysql_analysis)
    with open('mysql_summary_report.md', 'w', encoding='utf-8') as f:
        f.write(mysql_summary)
    
    # 多产品对比
    analyses = {
        'Data Factory SSIS': ssis_analysis,
        'Azure Database for MySQL': mysql_analysis
    }
    exporter.export_comparison_report(analyses, 'azure_products_comparison.xlsx')
    
    print("所有数据导出完成！")

if __name__ == "__main__":
    main()
