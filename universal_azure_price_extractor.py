import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from bs4 import BeautifulSoup
import pandas as pd
from abc import ABC, abstractmethod

@dataclass
class PriceDetail:
    """价格详情"""
    hourly_price: float
    monthly_price: Optional[float] = None
    currency: str = "CNY"
    pricing_model: str = "pay-as-you-go"  # 现用现付、预留实例等

@dataclass
class ProductInstance:
    """产品实例基类"""
    name: str
    price: PriceDetail
    specifications: Dict[str, Any]  # 规格信息（vCore、内存等）

@dataclass
class PricingTable:
    """定价表格"""
    table_id: str
    title: str
    category: str  # 产品类别
    service_tier: str  # 服务层级
    instances: List[ProductInstance]

@dataclass
class RegionalPricingData:
    """区域定价数据"""
    region_id: str
    region_name: str
    config_status: str
    excluded_tables: List[str]
    available_tables: List[PricingTable]
    total_available_configurations: int
    service_level: str

@dataclass
class CompleteProductAnalysis:
    """完整的产品分析结果"""
    product_name: str
    extraction_timestamp: str
    total_regions: int
    full_service_regions: int
    partial_service_regions: int
    regional_data: List[RegionalPricingData]
    business_insights: Dict[str, Any]

class BaseProductExtractor(ABC):
    """产品提取器基类"""
    
    def __init__(self):
        self.currency_pattern = r'￥\s*([\d,\.]+)'
        self.region_mapping = {
            'north-china': '中国北部',
            'north-china2': '中国北部 2', 
            'north-china3': '中国北部 3',
            'east-china': '中国东部',
            'east-china2': '中国东部 2',
            'east-china3': '中国东部 3'
        }
    
    @abstractmethod
    def get_product_name(self) -> str:
        """获取产品名称"""
        pass
    
    @abstractmethod
    def get_table_id_pattern(self) -> str:
        """获取表格ID匹配模式"""
        pass
    
    @abstractmethod
    def parse_instance_from_row(self, row) -> Optional[ProductInstance]:
        """从表格行解析产品实例"""
        pass
    
    @abstractmethod
    def extract_table_metadata(self, table, table_id: str) -> Dict[str, str]:
        """提取表格元数据（标题、类别等）"""
        pass
    
    def extract_price_from_text(self, price_text: str) -> Optional[PriceDetail]:
        """从价格文本中提取价格信息"""
        try:
            # 匹配小时价格
            hourly_match = re.search(r'￥\s*([\d,\.]+)/小时', price_text)
            if not hourly_match:
                # 尝试匹配其他格式
                price_match = re.search(self.currency_pattern, price_text)
                if price_match:
                    price = float(price_match.group(1).replace(',', ''))
                    return PriceDetail(hourly_price=price, currency="CNY")
                return None
            
            hourly_price = float(hourly_match.group(1).replace(',', ''))
            
            # 匹配月度价格
            monthly_match = re.search(r'约￥([\d,\.]+)/月', price_text)
            monthly_price = float(monthly_match.group(1).replace(',', '')) if monthly_match else hourly_price * 24 * 31
            
            return PriceDetail(
                hourly_price=hourly_price,
                monthly_price=monthly_price,
                currency="CNY"
            )
        except Exception as e:
            print(f"价格解析错误: {price_text}, 错误: {e}")
            return None
    
    def discover_all_tables_in_html(self, soup: BeautifulSoup) -> Dict[str, PricingTable]:
        """发现HTML中所有的产品定价表格"""
        all_tables = {}
        pattern = self.get_table_id_pattern()
        
        # 查找所有匹配的表格
        tables = soup.find_all('table', {'id': True})
        
        for table in tables:
            table_id = table.get('id')
            if re.search(pattern, table_id):
                full_table_id = f"#{table_id}"
                pricing_table = self.extract_single_pricing_table(soup, full_table_id)
                if pricing_table and pricing_table.instances:
                    all_tables[full_table_id] = pricing_table
                    print(f"✓ 发现表格: {full_table_id} - {pricing_table.title}")
        
        print(f"\n总共发现 {len(all_tables)} 个有效的{self.get_product_name()}定价表格")
        return all_tables
    
    def extract_single_pricing_table(self, soup: BeautifulSoup, table_id: str) -> Optional[PricingTable]:
        """提取单个定价表格"""
        try:
            table = soup.find('table', {'id': table_id.replace('#', '')})
            if not table:
                return None
            
            # 提取表格元数据
            metadata = self.extract_table_metadata(table, table_id)
            
            # 解析表格行
            instances = []
            tbody = table.find('tbody')
            if not tbody:
                # 如果没有tbody，直接查找tr
                rows = table.find_all('tr')[1:]  # 跳过表头
            else:
                rows = tbody.find_all('tr')  # 不跳过，让parse_instance_from_row来判断
            
            for row in rows:
                instance = self.parse_instance_from_row(row)
                if instance:
                    instances.append(instance)
            
            return PricingTable(
                table_id=table_id,
                title=metadata.get('title', ''),
                category=metadata.get('category', ''),
                service_tier=metadata.get('service_tier', ''),
                instances=instances
            )
        except Exception as e:
            print(f"表格解析错误 {table_id}: {e}")
            return None
    
    def discover_regions_from_html(self, soup: BeautifulSoup) -> List[str]:
        """从HTML中发现所有可用的区域"""
        regions = []
        
        # 查找区域选择器
        region_options = soup.find_all('option', {'data-href': True})
        if not region_options:
            region_options = soup.find_all('a', {'data-href': True})
        
        for option in region_options:
            region_id = option.get('id') or option.get('data-href', '').replace('#', '')
            if region_id and region_id.startswith(('north-', 'east-')):
                regions.append(region_id)
        
        # 去重并排序
        regions = list(set(regions))
        
        if not regions:
            regions = ['north-china3', 'east-china3', 'east-china2', 'north-china2', 'east-china', 'north-china']
            print("⚠️ 未能从HTML中解析区域，使用默认区域列表")
        else:
            print(f"✓ 从HTML中发现 {len(regions)} 个区域: {', '.join(regions)}")
        
        return regions
    
    def load_region_configuration(self, config_path: str) -> Dict[str, List[str]]:
        """加载区域配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            product_name = self.get_product_name()
            region_exclusion_mapping = {}
            
            for item in config:
                if item.get('os') == product_name:
                    region_id = item.get('region', '')
                    excluded_table_ids = item.get('tableIDs', [])
                    region_exclusion_mapping[region_id] = excluded_table_ids
                    
            return region_exclusion_mapping
        except Exception as e:
            print(f"区域配置加载错误: {e}")
            return {}
    
    def extract_all_regional_pricing(self, html_content: str, config_path: str) -> CompleteProductAnalysis:
        """提取所有区域的定价数据"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        print(f"=== 开始{self.get_product_name()}区域定价数据提取 ===")
        print("🔍 区域配置逻辑:")
        print("   1. 配置文件中有排除列表 → 排除指定表格")
        print("   2. 配置文件中为空数组 → 不排除任何表格")  
        print("   3. 配置文件中没有配置 → 不排除任何表格")
        print("")
        
        # 发现所有表格和区域
        all_tables = self.discover_all_tables_in_html(soup)
        all_table_ids = list(all_tables.keys())
        all_regions = self.discover_regions_from_html(soup)
        region_exclusions = self.load_region_configuration(config_path)
        
        print(f"\n配置文件中的{self.get_product_name()}排除规则: {len(region_exclusions)}个区域")
        for region_id, excludes in region_exclusions.items():
            print(f"  - {region_id}: {len(excludes)}个排除表格")
        print("")
        
        # 为每个区域计算实际可用的表格
        regional_data = []
        full_service_count = 0
        partial_service_count = 0
        
        for region_id in all_regions:
            region_name = self.region_mapping.get(region_id, region_id)
            
            # 确定配置状态和排除列表
            if region_id in region_exclusions:
                excluded_table_ids = region_exclusions[region_id]
                if len(excluded_table_ids) == 0:
                    config_status = "✅ 配置为空数组"
                else:
                    config_status = f"⚠️ 配置排除 {len(excluded_table_ids)} 个表格"
            else:
                excluded_table_ids = []
                config_status = "✅ 无配置（默认全部显示）"
            
            # 计算该区域可用的表格
            available_table_ids = [tid for tid in all_table_ids if tid not in excluded_table_ids]
            available_tables = [all_tables[tid] for tid in available_table_ids]
            
            total_configurations = sum(len(table.instances) for table in available_tables)
            
            # 确定服务级别
            service_level = self._determine_service_level(len(available_tables), total_configurations)
            if "完整服务" in service_level:
                full_service_count += 1
            elif any(x in service_level for x in ["标准服务", "基础服务"]):
                partial_service_count += 1
            
            regional_data.append(RegionalPricingData(
                region_id=region_id,
                region_name=region_name,
                config_status=config_status,
                excluded_tables=excluded_table_ids,
                available_tables=available_tables,
                total_available_configurations=total_configurations,
                service_level=service_level
            ))
            
            print(f"📍 {region_name}:")
            print(f"   {config_status}")
            print(f"   {service_level}")
            print(f"   排除表格: {len(excluded_table_ids)}个")
            print(f"   可用表格: {len(available_tables)}个") 
            print(f"   实例配置: {total_configurations}个")
            print("")
        
        # 生成业务洞察
        business_insights = self._generate_business_insights(regional_data)
        
        return CompleteProductAnalysis(
            product_name=self.get_product_name(),
            extraction_timestamp=datetime.now().isoformat(),
            total_regions=len(regional_data),
            full_service_regions=full_service_count,
            partial_service_regions=partial_service_count,
            regional_data=regional_data,
            business_insights=business_insights
        )
    
    def _determine_service_level(self, table_count: int, config_count: int) -> str:
        """确定服务级别"""
        if table_count >= 8:
            return "🟢 完整服务"
        elif table_count >= 4:
            return "🟡 标准服务"
        elif table_count >= 2:
            return "🟠 基础服务"
        else:
            return "🔴 无服务"
    
    def _generate_business_insights(self, regional_data: List[RegionalPricingData]) -> Dict[str, Any]:
        """生成业务洞察"""
        full_service_regions = [r.region_name for r in regional_data if "完整服务" in r.service_level]
        partial_service_regions = [r.region_name for r in regional_data if any(x in r.service_level for x in ["基础服务", "标准服务"])]
        
        configured_regions = [r.region_name for r in regional_data if "配置排除" in r.config_status]
        unconfigured_regions = [r.region_name for r in regional_data if "无配置" in r.config_status or "空数组" in r.config_status]
        
        recommendations = []
        if full_service_regions:
            recommendations.append(f"推荐完整服务区域: {', '.join(full_service_regions)}")
            recommendations.append("这些区域提供全面的功能，适合所有类型的工作负载")
        
        if partial_service_regions:
            recommendations.append(f"受限服务区域: {', '.join(partial_service_regions)}")
            recommendations.append("这些区域提供有限配置，适合特定需求")
        
        return {
            "full_service_regions": full_service_regions,
            "partial_service_regions": partial_service_regions,
            "configured_regions": configured_regions,
            "unconfigured_regions": unconfigured_regions,
            "recommendations": recommendations,
            "strategy_analysis": "新区域默认提供完整服务，老区域通过配置进行差异化管理"
        }
