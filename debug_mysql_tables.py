#!/usr/bin/env python3
"""
调试MySQL表格解析问题
"""

from bs4 import BeautifulSoup
import re

def debug_mysql_tables():
    """调试MySQL表格解析"""
    
    # 读取HTML文件
    with open('mysql-index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 查找所有MySQL表格
    pattern = r"Azure_Database_For_MySQL"
    tables = soup.find_all('table', {'id': True})
    
    mysql_tables = []
    for table in tables:
        table_id = table.get('id')
        if re.search(pattern, table_id):
            mysql_tables.append((table_id, table))
    
    print(f"发现 {len(mysql_tables)} 个MySQL表格:")
    for table_id, table in mysql_tables:
        print(f"  - {table_id}")
    
    print("\n" + "="*80)
    
    # 详细分析每个表格
    for table_id, table in mysql_tables:
        print(f"\n🔍 分析表格: {table_id}")
        
        # 查找标题
        title_element = table.find_previous(['h2', 'h3', 'h4'])
        title = title_element.get_text(strip=True) if title_element else "无标题"
        print(f"   标题: {title}")
        
        # 分析表格结构
        tbody = table.find('tbody')
        if not tbody:
            rows = table.find_all('tr')[1:]  # 跳过表头
        else:
            rows = tbody.find_all('tr')[1:]  # 跳过表头
        
        print(f"   数据行数: {len(rows)}")
        
        # 分析每一行
        for i, row in enumerate(rows):
            cells = row.find_all(['td', 'th'])
            if len(cells) < 2:
                continue
                
            # 跳过表头行
            if any(cell.find('strong') for cell in cells):
                continue
                
            name = cells[0].get_text(strip=True)
            if not name or name in ['实例', '']:
                continue
            
            print(f"     行{i+1}: '{name}' ({len(cells)}列)")
            
            # 显示所有列的内容
            for j, cell in enumerate(cells):
                content = cell.get_text(strip=True)
                print(f"       列{j+1}: '{content}'")
        
        print("-" * 60)

def check_specific_missing_tables():
    """检查特定缺失的表格"""
    
    with open('mysql-index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    missing_tables = [
        'Azure_Database_For_MySQL9',   # 存储
        'Azure_Database_For_MySQL3',   # 其他IOPS  
        'Azure_Database_For_MySQL13'   # 备份
    ]
    
    print("🔍 检查特定缺失的表格:")
    
    for table_id in missing_tables:
        table = soup.find('table', {'id': table_id})
        if table:
            print(f"\n✅ 找到表格: {table_id}")
            
            # 查找标题
            title_element = table.find_previous(['h2', 'h3', 'h4'])
            title = title_element.get_text(strip=True) if title_element else "无标题"
            print(f"   标题: {title}")
            
            # 分析表格内容
            tbody = table.find('tbody')
            if not tbody:
                rows = table.find_all('tr')[1:]
            else:
                rows = tbody.find_all('tr')[1:]
            
            print(f"   数据行数: {len(rows)}")
            
            for i, row in enumerate(rows):
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    name = cells[0].get_text(strip=True)
                    price = cells[1].get_text(strip=True) if len(cells) > 1 else ""
                    print(f"     行{i+1}: '{name}' -> '{price}'")
        else:
            print(f"❌ 未找到表格: {table_id}")

def check_d2ds_v4_issue():
    """检查D2ds v4缺失问题"""
    
    with open('mysql-index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 检查Azure_Database_For_MySQL6表格
    table = soup.find('table', {'id': 'Azure_Database_For_MySQL6'})
    if table:
        print("🔍 检查Azure_Database_For_MySQL6表格中的D2ds v4:")
        
        tbody = table.find('tbody')
        if not tbody:
            rows = table.find_all('tr')[1:]
        else:
            rows = tbody.find_all('tr')[1:]
        
        found_d2ds = False
        for i, row in enumerate(rows):
            cells = row.find_all(['td', 'th'])
            if len(cells) >= 2:
                name = cells[0].get_text(strip=True)
                if 'D2ds v4' in name:
                    found_d2ds = True
                    print(f"   ✅ 找到D2ds v4在行{i+1}: {name}")
                    for j, cell in enumerate(cells):
                        content = cell.get_text(strip=True)
                        print(f"     列{j+1}: '{content}'")
        
        if not found_d2ds:
            print("   ❌ 未找到D2ds v4")
            print("   所有实例:")
            for i, row in enumerate(rows):
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    name = cells[0].get_text(strip=True)
                    if name and name not in ['实例', '']:
                        print(f"     行{i+1}: {name}")

if __name__ == "__main__":
    print("🔍 MySQL表格调试分析")
    print("=" * 80)
    
    # 1. 总体表格发现
    debug_mysql_tables()
    
    print("\n" + "="*80)
    
    # 2. 检查特定缺失表格
    check_specific_missing_tables()
    
    print("\n" + "="*80)
    
    # 3. 检查D2ds v4问题
    check_d2ds_v4_issue()
