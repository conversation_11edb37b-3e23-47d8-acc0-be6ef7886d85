import re
from typing import Optional, Dict, Any
from dataclasses import dataclass
from universal_azure_price_extractor import BaseProductExtractor, ProductInstance, PriceDetail

@dataclass
class CosmosDBInstance(ProductInstance):
    """Cosmos DB实例"""
    ru_per_second: Optional[int] = None  # 请求单位/秒
    storage_type: Optional[str] = None  # 存储类型
    backup_type: Optional[str] = None  # 备份类型
    transaction_type: Optional[str] = None  # 事务类型
    
    def __post_init__(self):
        # 确保specifications字典包含所有字段
        if not hasattr(self, 'specifications') or self.specifications is None:
            self.specifications = {}
        
        self.specifications.update({
            'ru_per_second': self.ru_per_second,
            'storage_type': self.storage_type,
            'backup_type': self.backup_type,
            'transaction_type': self.transaction_type
        })

class CosmosDBExtractor(BaseProductExtractor):
    """Azure Cosmos DB 价格提取器"""
    
    def get_product_name(self) -> str:
        return "Azure Cosmos DB"
    
    def get_table_id_pattern(self) -> str:
        return r"cosmos-db|cosmos-\d+"
    
    def parse_instance_from_row(self, row) -> Optional[ProductInstance]:
        """解析Cosmos DB实例行"""
        try:
            cells = row.find_all(['td', 'th'])
            if len(cells) < 2:
                return None
            
            # 跳过表头行
            if any(cell.find('strong') for cell in cells):
                return None
            
            name = cells[0].get_text(strip=True)
            if not name or name in ['实例', '']:
                return None
            
            # 根据表格类型解析不同的列结构
            if len(cells) >= 3:  # 标准3列表格
                return self._parse_standard_instance(cells)
            elif len(cells) >= 2:  # 2列表格
                return self._parse_simple_instance(cells)
            
            return None
            
        except Exception as e:
            print(f"Cosmos DB实例解析错误: {e}")
            return None
    
    def _parse_standard_instance(self, cells) -> Optional[CosmosDBInstance]:
        """解析标准实例（3列或更多）"""
        try:
            name = cells[0].get_text(strip=True)
            
            # 解析第二列（通常是规格或数量）
            spec_text = cells[1].get_text(strip=True)
            
            # 解析价格（通常在最后一列）
            price_text = cells[-1].get_text(strip=True)
            price = self.extract_price_from_text(price_text)
            
            if not price:
                return None
            
            # 解析RU/s信息
            ru_per_second = None
            ru_match = re.search(r'(\d+)\s*RU/s', spec_text)
            if ru_match:
                ru_per_second = int(ru_match.group(1))
            
            # 确定实例类型
            storage_type, backup_type, transaction_type = self._determine_instance_type(name)
            
            return CosmosDBInstance(
                name=name,
                price=price,
                ru_per_second=ru_per_second,
                storage_type=storage_type,
                backup_type=backup_type,
                transaction_type=transaction_type,
                specifications={}
            )
            
        except Exception as e:
            print(f"标准实例解析错误: {e}")
            return None
    
    def _parse_simple_instance(self, cells) -> Optional[CosmosDBInstance]:
        """解析简单实例（2列）"""
        try:
            name = cells[0].get_text(strip=True)
            price_text = cells[1].get_text(strip=True)
            
            price = self.extract_price_from_text(price_text)
            if not price:
                return None
            
            # 确定实例类型
            storage_type, backup_type, transaction_type = self._determine_instance_type(name)
            
            return CosmosDBInstance(
                name=name,
                price=price,
                storage_type=storage_type,
                backup_type=backup_type,
                transaction_type=transaction_type,
                specifications={}
            )
            
        except Exception as e:
            print(f"简单实例解析错误: {e}")
            return None
    
    def _determine_instance_type(self, name: str) -> tuple:
        """确定实例类型"""
        name_lower = name.lower()
        
        # 存储类型
        storage_type = None
        if "事务存储" in name or "行导向" in name:
            storage_type = "事务存储"
        elif "分析存储" in name or "列导向" in name:
            storage_type = "分析存储"
        
        # 备份类型
        backup_type = None
        if "定期备份" in name:
            backup_type = "定期备份"
        elif "连续备份" in name:
            backup_type = "连续备份"
        elif "时间点还原" in name:
            backup_type = "时间点还原"
        
        # 事务类型
        transaction_type = None
        if "写入操作" in name:
            transaction_type = "写入操作"
        elif "读取操作" in name:
            transaction_type = "读取操作"
        
        return storage_type, backup_type, transaction_type
    
    def extract_table_metadata(self, table, table_id: str) -> Dict[str, str]:
        """提取Cosmos DB表格元数据"""
        try:
            # 查找表格标题
            title_element = table.find_previous(['h2', 'h3', 'h4'])
            title = title_element.get_text(strip=True) if title_element else ""
            
            # 根据标题和ID确定类别和服务层级
            category = self._determine_category(title, table_id)
            service_tier = self._determine_service_tier(title, table_id)
            
            return {
                'title': title,
                'category': category,
                'service_tier': service_tier
            }
            
        except Exception as e:
            print(f"Cosmos DB元数据提取错误: {e}")
            return {'title': '', 'category': '', 'service_tier': ''}
    
    def _determine_category(self, title: str, table_id: str) -> str:
        """确定产品类别"""
        title_lower = title.lower()
        
        if "吞吐量" in title or "ru/s" in title_lower:
            return "吞吐量"
        elif "存储" in title:
            return "存储"
        elif "备份" in title:
            return "备份"
        elif "事务" in title:
            return "事务"
        elif "带宽" in title or "数据传输" in title:
            return "带宽"
        else:
            return "其他"
    
    def _determine_service_tier(self, title: str, table_id: str) -> str:
        """确定服务层级"""
        if "标准" in title:
            return "标准"
        elif "自动缩放" in title:
            return "自动缩放"
        elif "无服务器" in title:
            return "无服务器"
        else:
            return "标准"
    
    def extract_price_from_text(self, price_text: str) -> Optional[PriceDetail]:
        """Cosmos DB特定的价格解析"""
        try:
            # 清理文本
            price_text = price_text.replace('\n', ' ').replace('\r', ' ')
            
            # 匹配各种价格格式
            patterns = [
                r'￥([\d,\.]+)/小时',  # ￥ 0.051/小时
                r'￥\s*([\d,\.]+)\s*/\s*小时',  # ￥ 0.051 / 小时
                r'￥\s*([\d,\.]+)/月',  # ￥ 2.576/月
                r'￥\s*([\d,\.]+)\s*/\s*月',  # ￥ 2.576 / 月
                r'￥([\d,\.]+)',  # ￥ 0.051
            ]
            
            for pattern in patterns:
                match = re.search(pattern, price_text)
                if match:
                    price = float(match.group(1).replace(',', ''))
                    
                    # 根据价格类型确定定价模式
                    if '/小时' in price_text:
                        pricing_model = "按小时计费"
                        monthly_price = price * 24 * 31
                    elif '/月' in price_text:
                        pricing_model = "按月计费"
                        monthly_price = price
                    elif '免费' in price_text:
                        pricing_model = "免费"
                        monthly_price = 0
                        price = 0
                    else:
                        pricing_model = "其他"
                        monthly_price = price * 24 * 31  # 默认按小时计算
                    
                    return PriceDetail(
                        hourly_price=price,
                        monthly_price=monthly_price,
                        currency="CNY",
                        pricing_model=pricing_model
                    )
            
            # 处理免费情况
            if '免费' in price_text:
                return PriceDetail(
                    hourly_price=0,
                    monthly_price=0,
                    currency="CNY",
                    pricing_model="免费"
                )
            
            return None
            
        except Exception as e:
            print(f"Cosmos DB价格解析错误: {price_text}, 错误: {e}")
            return None

# 使用示例
def main():
    """Cosmos DB提取器使用示例"""
    extractor = CosmosDBExtractor()
    
    # 读取HTML文件
    with open('prod-html/cosmos-db-index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 提取所有区域的定价数据
    print("=== 开始提取Cosmos DB区域定价数据 ===")
    analysis = extractor.extract_all_regional_pricing(html_content, 'soft-category.json')
    
    # 输出结果
    print(f"\n=== 提取完成 ===")
    print(f"总区域数: {analysis.total_regions}")
    print(f"完整服务区域: {analysis.full_service_regions}个")
    print(f"受限服务区域: {analysis.partial_service_regions}个")
    
    print(f"\n=== 区域服务级别 ===")
    for region in analysis.regional_data:
        print(f"{region.region_name}: {region.service_level}")
        print(f"  └─ {region.config_status}")
        print(f"  └─ 可用表格: {len(region.available_tables)}个")
        print(f"  └─ 实例配置: {region.total_available_configurations}个")

if __name__ == "__main__":
    main()
