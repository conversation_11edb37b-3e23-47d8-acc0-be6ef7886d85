#!/usr/bin/env python3
"""
调试MySQL解析问题的详细脚本
"""

from bs4 import BeautifulSoup
import re

def debug_specific_table(table_id):
    """调试特定表格的解析问题"""
    
    with open('mysql-index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    table = soup.find('table', {'id': table_id})
    
    if not table:
        print(f"❌ 未找到表格: {table_id}")
        return
    
    print(f"🔍 调试表格: {table_id}")
    
    # 查找标题
    title_element = table.find_previous(['h2', 'h3', 'h4'])
    title = title_element.get_text(strip=True) if title_element else "无标题"
    print(f"   标题: {title}")
    
    # 分析表格结构
    print(f"   表格HTML结构:")
    print(f"   - 有thead: {table.find('thead') is not None}")
    print(f"   - 有tbody: {table.find('tbody') is not None}")
    
    # 获取所有行
    all_rows = table.find_all('tr')
    print(f"   - 总行数: {len(all_rows)}")
    
    # 分析每一行
    for i, row in enumerate(all_rows):
        cells = row.find_all(['td', 'th'])
        print(f"   行{i+1}: {len(cells)}列")
        
        # 检查是否是表头行
        has_strong = any(cell.find('strong') for cell in cells)
        print(f"     - 包含<strong>: {has_strong}")
        
        if len(cells) > 0:
            first_cell_text = cells[0].get_text(strip=True)
            print(f"     - 第一列内容: '{first_cell_text}'")
            
            # 显示所有列内容
            for j, cell in enumerate(cells):
                content = cell.get_text(strip=True)
                print(f"       列{j+1}: '{content}'")
        
        print()

def debug_mysql_extractor_logic():
    """调试MySQL提取器的逻辑"""
    
    from mysql_extractor import MySQLExtractor
    
    extractor = MySQLExtractor()
    
    with open('mysql-index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 测试特定表格
    test_tables = [
        'Azure_Database_For_MySQL6',  # D系列 - 应该有D2ds v4
        'Azure_Database_For_MySQL4',  # E系列 - 应该有E2ds v4
        'Azure_Database_For_MySQL9',  # 存储
        'Azure_Database_For_MySQL3',  # 其他IOPS
        'Azure_Database_For_MySQL13'  # 备份
    ]
    
    for table_id in test_tables:
        print(f"\n🔍 测试提取器对表格 {table_id} 的处理:")
        
        full_table_id = f"#{table_id}"
        pricing_table = extractor.extract_single_pricing_table(soup, full_table_id)
        
        if pricing_table:
            print(f"   ✅ 成功提取表格")
            print(f"   - 标题: {pricing_table.title}")
            print(f"   - 类别: {pricing_table.category}")
            print(f"   - 服务层级: {pricing_table.service_tier}")
            print(f"   - 实例数: {len(pricing_table.instances)}")
            
            for i, instance in enumerate(pricing_table.instances):
                print(f"     实例{i+1}: {instance.name} - ￥{instance.price.hourly_price}/小时")
        else:
            print(f"   ❌ 提取失败")
            
            # 手动分析原因
            table = soup.find('table', {'id': table_id})
            if table:
                tbody = table.find('tbody')
                if not tbody:
                    rows = table.find_all('tr')[1:]
                else:
                    rows = tbody.find_all('tr')[1:]
                
                print(f"   - 原始数据行数: {len(rows)}")
                
                valid_rows = 0
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) < 2:
                        continue
                    
                    # 跳过表头行
                    if any(cell.find('strong') for cell in cells):
                        continue
                    
                    name = cells[0].get_text(strip=True)
                    if not name or name in ['实例', '']:
                        continue
                    
                    valid_rows += 1
                    print(f"     有效行: '{name}' ({len(cells)}列)")
                
                print(f"   - 有效数据行数: {valid_rows}")

def test_price_extraction():
    """测试价格提取逻辑"""
    
    from mysql_extractor import MySQLExtractor
    
    extractor = MySQLExtractor()
    
    test_prices = [
        "￥ 1.1220/小时",
        "￥ 0.8100",
        "￥ 0.3528",
        "每百万 IOPS ￥2.04",
        "GB/月"
    ]
    
    print("🔍 测试价格提取逻辑:")
    
    for price_text in test_prices:
        result = extractor.extract_price_from_text(price_text)
        print(f"   '{price_text}' -> {result}")

if __name__ == "__main__":
    print("🔍 MySQL解析问题详细调试")
    print("=" * 80)
    
    # 1. 调试特定表格
    print("\n1. 调试Azure_Database_For_MySQL6 (D系列):")
    debug_specific_table('Azure_Database_For_MySQL6')
    
    print("\n" + "="*80)
    print("\n2. 调试Azure_Database_For_MySQL9 (存储):")
    debug_specific_table('Azure_Database_For_MySQL9')
    
    print("\n" + "="*80)
    print("\n3. 测试MySQL提取器逻辑:")
    debug_mysql_extractor_logic()
    
    print("\n" + "="*80)
    print("\n4. 测试价格提取逻辑:")
    test_price_extraction()
