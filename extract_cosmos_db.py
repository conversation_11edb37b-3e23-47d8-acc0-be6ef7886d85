#!/usr/bin/env python3
"""
Azure Cosmos DB 数据提取器 - 使用AI增强提取器
"""

import os
import json
from datetime import datetime
from ai_enhanced_extractor import AdaptiveExtractor, AITableAnalyzer
from data_exporter import UniversalDataExporter

class CosmosDBExtractor:
    """Cosmos DB专用提取器"""
    
    def __init__(self):
        self.adaptive_extractor = AdaptiveExtractor()
        self.ai_analyzer = AITableAnalyzer()
        self.exporter = UniversalDataExporter()
    
    def extract_cosmos_db_data(self, html_file_path: str, output_dir: str = "output"):
        """提取Cosmos DB数据"""
        
        print("🌌 Azure Cosmos DB 数据提取器")
        print("=" * 60)
        
        # 检查文件是否存在
        if not os.path.exists(html_file_path):
            print(f"❌ 文件不存在: {html_file_path}")
            return None
        
        # 读取HTML文件
        print(f"📖 读取HTML文件: {html_file_path}")
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 使用AI增强提取器进行自适应提取
        print("🤖 开始AI增强自适应提取...")
        result = self.adaptive_extractor.extract_unknown_product(html_content, "Azure Cosmos DB")
        
        # 显示提取结果概览
        print(f"\n📊 提取结果概览:")
        print(f"   发现表格: {result['total_tables_found']}个")
        print(f"   成功提取: {result['successfully_extracted']}个")
        print(f"   提取方法: {result['extraction_method']}")
        
        # 详细分析每个表格
        self._analyze_extraction_results(result)
        
        # 导出数据
        self._export_cosmos_db_data(result, output_dir)
        
        return result
    
    def _analyze_extraction_results(self, result):
        """分析提取结果"""
        print(f"\n🔍 详细分析:")
        
        if 'analysis_results' in result:
            for i, analysis in enumerate(result['analysis_results']):
                print(f"\n  📋 表格 {i+1}: {analysis['table_id']}")
                print(f"     置信度: {analysis['confidence_score']:.2f}")
                print(f"     结构: {analysis['column_count']}列 x {analysis['row_count']}行")
                
                # 显示检测到的模式
                patterns = analysis['detected_patterns']
                print(f"     检测模式:")
                print(f"       - 价格列: {patterns.get('price_columns', [])}")
                print(f"       - 名称列: {patterns.get('name_columns', [])}")
                print(f"       - 规格列: {patterns.get('spec_columns', [])}")
                
                # 显示表头信息
                if analysis['header_row']:
                    print(f"     表头: {', '.join(analysis['header_row'][:4])}{'...' if len(analysis['header_row']) > 4 else ''}")
        
        # 显示成功提取的表格
        print(f"\n✅ 成功提取的表格:")
        for i, table in enumerate(result['tables']):
            print(f"   {i+1}. {table['table_id']}")
            print(f"      标题: {table['title']}")
            print(f"      类别: {table['category']}")
            print(f"      实例数: {len(table['instances'])}")
            
            # 显示前几个实例作为样本
            if table['instances']:
                print(f"      样本实例:")
                for j, instance in enumerate(table['instances'][:3]):
                    print(f"        - {instance['name']}: {instance.get('price', {})}")
                if len(table['instances']) > 3:
                    print(f"        ... 还有 {len(table['instances']) - 3} 个实例")
    
    def _export_cosmos_db_data(self, result, output_dir):
        """导出Cosmos DB数据"""
        print(f"\n📤 导出数据到 {output_dir} 目录...")
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 1. 导出原始JSON数据
        json_file = os.path.join(output_dir, "cosmos_db_raw_data.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"   ✅ 原始数据: {json_file}")
        
        # 2. 导出结构化Excel报告
        excel_file = os.path.join(output_dir, "cosmos_db_pricing_analysis.xlsx")
        self._export_to_excel(result, excel_file)
        print(f"   ✅ Excel报告: {excel_file}")
        
        # 3. 导出Markdown摘要
        md_file = os.path.join(output_dir, "cosmos_db_summary_report.md")
        self._export_markdown_summary(result, md_file)
        print(f"   ✅ 摘要报告: {md_file}")
        
        # 4. 导出分析报告
        analysis_file = os.path.join(output_dir, "cosmos_db_analysis_report.json")
        self._export_analysis_report(result, analysis_file)
        print(f"   ✅ 分析报告: {analysis_file}")
    
    def _export_to_excel(self, result, excel_file):
        """导出到Excel"""
        import pandas as pd
        
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            
            # 概览表
            overview_data = []
            for i, table in enumerate(result['tables']):
                overview_data.append({
                    '表格ID': table['table_id'],
                    '标题': table['title'],
                    '类别': table['category'],
                    '服务层级': table.get('service_tier', 'auto-detected'),
                    '实例数': len(table['instances']),
                    '置信度': table.get('extraction_metadata', {}).get('confidence', 'N/A')
                })
            
            overview_df = pd.DataFrame(overview_data)
            overview_df.to_excel(writer, sheet_name='概览', index=False)
            
            # 详细数据表
            all_instances = []
            for table in result['tables']:
                for instance in table['instances']:
                    row = {
                        '表格ID': table['table_id'],
                        '表格标题': table['title'],
                        '类别': table['category'],
                        '实例名称': instance['name'],
                    }
                    
                    # 添加价格信息
                    price_data = instance.get('price', {})
                    for key, value in price_data.items():
                        row[f'价格_{key}'] = value
                    
                    # 添加属性信息
                    attributes = instance.get('attributes', {})
                    for key, value in attributes.items():
                        if isinstance(value, dict) and 'value' in value:
                            row[f'属性_{key}'] = value['value']
                        else:
                            row[f'属性_{key}'] = value
                    
                    all_instances.append(row)
            
            if all_instances:
                instances_df = pd.DataFrame(all_instances)
                instances_df.to_excel(writer, sheet_name='详细数据', index=False)
    
    def _export_markdown_summary(self, result, md_file):
        """导出Markdown摘要"""
        
        summary = f"""# Azure Cosmos DB 定价数据提取报告

## 基本信息
- 产品: Azure Cosmos DB
- 提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 提取方法: {result['extraction_method']}
- 发现表格: {result['total_tables_found']}个
- 成功提取: {result['successfully_extracted']}个

## 提取结果详情

### 表格概览
"""
        
        for i, table in enumerate(result['tables']):
            summary += f"""
#### 表格 {i+1}: {table['title']}
- **表格ID**: {table['table_id']}
- **类别**: {table['category']}
- **实例数**: {len(table['instances'])}
- **置信度**: {table.get('extraction_metadata', {}).get('confidence', 'N/A')}

"""
            
            # 添加实例样本
            if table['instances']:
                summary += "**实例样本**:\n"
                for j, instance in enumerate(table['instances'][:5]):
                    price_info = instance.get('price', {})
                    price_str = ""
                    if 'hourly_price' in price_info:
                        price_str = f"￥{price_info['hourly_price']}/小时"
                    elif 'unit_price' in price_info:
                        price_str = f"￥{price_info['unit_price']}"
                    elif 'price' in price_info:
                        price_str = f"￥{price_info['price']}"
                    
                    summary += f"- {instance['name']}: {price_str}\n"
                
                if len(table['instances']) > 5:
                    summary += f"- ... 还有 {len(table['instances']) - 5} 个实例\n"
        
        # 添加AI分析结果
        if 'analysis_results' in result:
            summary += f"""
## AI分析结果

### 表格结构分析
"""
            for analysis in result['analysis_results']:
                summary += f"""
#### {analysis['table_id']}
- 置信度: {analysis['confidence_score']:.2f}
- 结构: {analysis['column_count']}列 x {analysis['row_count']}行
- 检测到的价格列: {analysis['detected_patterns'].get('price_columns', [])}
- 检测到的名称列: {analysis['detected_patterns'].get('name_columns', [])}
- 检测到的规格列: {analysis['detected_patterns'].get('spec_columns', [])}
"""
        
        summary += f"""
## 数据质量评估

### 总体统计
- 总表格数: {len(result['tables'])}
- 总实例数: {sum(len(table['instances']) for table in result['tables'])}
- 平均每表格实例数: {sum(len(table['instances']) for table in result['tables']) / len(result['tables']) if result['tables'] else 0:.1f}

### 建议
1. 手动验证高置信度表格的数据准确性
2. 对低置信度表格进行人工审核
3. 检查价格格式的一致性
4. 验证实例名称的完整性

---
*本报告由AI增强提取器自动生成*
"""
        
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(summary)
    
    def _export_analysis_report(self, result, analysis_file):
        """导出分析报告"""
        
        analysis_report = {
            'extraction_metadata': {
                'product_name': 'Azure Cosmos DB',
                'extraction_time': datetime.now().isoformat(),
                'extraction_method': result['extraction_method'],
                'total_tables_found': result['total_tables_found'],
                'successfully_extracted': result['successfully_extracted']
            },
            'quality_metrics': {
                'average_confidence': 0,
                'high_confidence_tables': 0,
                'medium_confidence_tables': 0,
                'low_confidence_tables': 0
            },
            'data_statistics': {
                'total_tables': len(result['tables']),
                'total_instances': sum(len(table['instances']) for table in result['tables']),
                'tables_with_instances': sum(1 for table in result['tables'] if table['instances']),
                'empty_tables': sum(1 for table in result['tables'] if not table['instances'])
            },
            'table_analysis': [],
            'recommendations': []
        }
        
        # 计算质量指标
        if 'analysis_results' in result:
            confidences = [analysis['confidence_score'] for analysis in result['analysis_results']]
            if confidences:
                analysis_report['quality_metrics']['average_confidence'] = sum(confidences) / len(confidences)
                analysis_report['quality_metrics']['high_confidence_tables'] = sum(1 for c in confidences if c >= 0.8)
                analysis_report['quality_metrics']['medium_confidence_tables'] = sum(1 for c in confidences if 0.6 <= c < 0.8)
                analysis_report['quality_metrics']['low_confidence_tables'] = sum(1 for c in confidences if c < 0.6)
        
        # 表格分析
        for table in result['tables']:
            table_analysis = {
                'table_id': table['table_id'],
                'title': table['title'],
                'category': table['category'],
                'instances_count': len(table['instances']),
                'has_price_data': any('price' in instance for instance in table['instances']),
                'extraction_metadata': table.get('extraction_metadata', {})
            }
            analysis_report['table_analysis'].append(table_analysis)
        
        # 生成建议
        if analysis_report['quality_metrics']['low_confidence_tables'] > 0:
            analysis_report['recommendations'].append("建议人工审核低置信度表格")
        
        if analysis_report['data_statistics']['empty_tables'] > 0:
            analysis_report['recommendations'].append("检查空表格是否为解析错误")
        
        if analysis_report['data_statistics']['total_instances'] > 100:
            analysis_report['recommendations'].append("数据量较大，建议进行抽样验证")
        
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_report, f, ensure_ascii=False, indent=2)

def main():
    """主程序"""
    extractor = CosmosDBExtractor()
    
    # 提取Cosmos DB数据
    html_file = "prod-html/cosmos-db-index.html"
    output_dir = "output"
    
    result = extractor.extract_cosmos_db_data(html_file, output_dir)
    
    if result:
        print(f"\n🎉 Cosmos DB数据提取完成！")
        print(f"   输出目录: {output_dir}")
        print(f"   成功提取: {result['successfully_extracted']} / {result['total_tables_found']} 个表格")
    else:
        print(f"❌ 数据提取失败")

if __name__ == "__main__":
    main()
