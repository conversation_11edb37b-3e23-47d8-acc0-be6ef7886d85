#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Azure定价数据提取器 - 完整版本
支持多种Azure产品的定价数据提取和区域分析

作者: Azure定价重建项目团队
版本: 1.0.0
日期: 2025-06-20
"""

import json
import re
import os
import glob
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict, field
from bs4 import BeautifulSoup
import pandas as pd
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ================================
# 数据模型定义
# ================================

@dataclass
class PriceDetail:
    """价格详情"""
    hourly_price: float
    monthly_price: Optional[float] = None
    currency: str = "CNY"
    unit: str = "小时"
    
    def __post_init__(self):
        if self.monthly_price is None:
            self.monthly_price = self.hourly_price * 24 * 31

@dataclass
class HybridBenefit:
    """混合优惠信息（主要用于SSIS）"""
    price: PriceDetail
    savings_percentage: int

@dataclass
class VMInstance:
    """虚拟机实例规格（SSIS）"""
    name: str
    vcores: int
    memory_gb: float
    temp_storage: str
    license_included_price: PriceDetail
    hybrid_benefit: Optional[HybridBenefit] = None
    vm_series: Optional[str] = None
    license_type: Optional[str] = None

@dataclass
class DatabaseInstance:
    """数据库实例规格（MySQL等）"""
    name: str
    vcores: int
    memory: str
    tier: str  # 可突增/常规用途/业务关键
    series: Optional[str] = None
    price: PriceDetail = None

@dataclass
class ServicePricing:
    """服务定价（存储、IOPS、备份等）"""
    service_type: str
    price: str
    unit: str
    description: Optional[str] = None
    region_specific: bool = False

@dataclass
class PricingTable:
    """定价表格"""
    table_id: str
    title: str
    category: str  # compute/storage/iops/backup/hybrid
    tier: Optional[str] = None
    vm_series: Optional[str] = None
    license_type: Optional[str] = None
    instances: List[Union[VMInstance, DatabaseInstance]] = field(default_factory=list)
    services: List[ServicePricing] = field(default_factory=list)
    region_specific: bool = False

@dataclass
class RegionConfig:
    """区域配置"""
    region_id: str
    region_name: str
    is_default: bool = False
    excluded_tables: List[str] = field(default_factory=list)
    available_tables: List[PricingTable] = field(default_factory=list)
    special_features: List[str] = field(default_factory=list)
    config_status: str = ""

@dataclass
class ProductInfo:
    """产品基本信息"""
    name: str
    english_name: str = ""
    description: str = ""
    service_type: str = ""
    icon_path: str = ""
    banner_config: Dict[str, Any] = field(default_factory=dict)

@dataclass
class BusinessInsights:
    """业务洞察"""
    cost_optimization_regions: List[str] = field(default_factory=list)
    full_feature_regions: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    price_comparison: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AzureProductAnalysis:
    """Azure产品完整分析"""
    product_info: ProductInfo
    product_type: str  # ssis/mysql/其他
    regions: List[RegionConfig]
    pricing_tables: List[PricingTable]
    business_insights: BusinessInsights
    extraction_timestamp: str
    metadata: Dict[str, Any] = field(default_factory=dict)

# ================================
# 核心提取器类
# ================================

class AzureProductPricingExtractor:
    """Azure产品定价数据提取器 - 通用版本"""
    
    def __init__(self):
        self.currency_pattern = r'￥\s*([\d,\.]+)'
        self.percentage_pattern = r'（~(\d+)%）'
        
        # 区域映射表
        self.region_mapping = {
            'north-china': '中国北部',
            'north-china2': '中国北部 2',
            'north-china3': '中国北部 3',
            'east-china': '中国东部',
            'east-china2': '中国东部 2',
            'east-china3': '中国东部 3'
        }
        
        # 产品类型检测模式
        self.product_patterns = {
            'ssis': ['data-factory-ssis', 'Integration Services', 'SSIS'],
            'mysql': ['Azure Database for MySQL', 'mysql', 'Database for MySQL'],
            'postgresql': ['PostgreSQL', 'Database for PostgreSQL'],
            'storage': ['Storage', 'Blob', 'File'],
            'vm': ['Virtual Machine', 'VM', '虚拟机']
        }
    
    def detect_product_type(self, soup: BeautifulSoup, html_content: str) -> str:
        """自动检测产品类型"""
        content_text = html_content.lower()
        
        for product_type, patterns in self.product_patterns.items():
            for pattern in patterns:
                if pattern.lower() in content_text:
                    logger.info(f"检测到产品类型: {product_type}")
                    return product_type
        
        logger.warning("未能检测到具体产品类型，使用通用处理")
        return 'generic'
    
    def extract_price_from_text(self, price_text: str) -> Optional[PriceDetail]:
        """从价格文本中提取价格信息"""
        try:
            # 提取小时价格
            hourly_match = re.search(r'￥\s*([\d,\.]+)/小时', price_text)
            if not hourly_match:
                # 尝试其他格式
                price_match = re.search(self.currency_pattern, price_text)
                if not price_match:
                    return None
                price_value = float(price_match.group(1).replace(',', ''))
                
                # 判断单位
                if '/月' in price_text:
                    return PriceDetail(
                        hourly_price=price_value / (24 * 31),
                        monthly_price=price_value,
                        currency="CNY",
                        unit="月"
                    )
                else:
                    return PriceDetail(
                        hourly_price=price_value,
                        currency="CNY"
                    )
            
            hourly_price = float(hourly_match.group(1).replace(',', ''))
            
            # 提取月度价格
            monthly_match = re.search(r'约￥([\d,\.]+)/月', price_text)
            monthly_price = float(monthly_match.group(1).replace(',', '')) if monthly_match else None
            
            return PriceDetail(
                hourly_price=hourly_price,
                monthly_price=monthly_price,
                currency="CNY"
            )
            
        except Exception as e:
            logger.error(f"价格解析错误: {price_text}, 错误: {e}")
            return None
    
    def extract_hybrid_benefit(self, benefit_text: str) -> Optional[HybridBenefit]:
        """提取混合优惠信息（主要用于SSIS）"""
        try:
            price = self.extract_price_from_text(benefit_text)
            if not price:
                return None
                
            percentage_match = re.search(self.percentage_pattern, benefit_text)
            savings_percentage = int(percentage_match.group(1)) if percentage_match else 0
            
            return HybridBenefit(
                price=price,
                savings_percentage=savings_percentage
            )
        except Exception as e:
            logger.error(f"混合优惠解析错误: {benefit_text}, 错误: {e}")
            return None
    
    def parse_ssis_vm_instance(self, row) -> Optional[VMInstance]:
        """解析SSIS虚拟机实例"""
        try:
            cells = row.find_all('td')
            if len(cells) < 5:
                return None
                
            name = cells[0].get_text(strip=True)
            vcores = int(cells[1].get_text(strip=True))
            
            memory_text = cells[2].get_text(strip=True)
            memory_gb = float(re.search(r'([\d\.]+)', memory_text).group(1))
            
            temp_storage = cells[3].get_text(strip=True)
            
            license_price = self.extract_price_from_text(cells[4].get_text())
            if not license_price:
                return None
                
            hybrid_benefit = None
            if len(cells) > 5:
                hybrid_benefit = self.extract_hybrid_benefit(cells[5].get_text())
            
            return VMInstance(
                name=name,
                vcores=vcores,
                memory_gb=memory_gb,
                temp_storage=temp_storage,
                license_included_price=license_price,
                hybrid_benefit=hybrid_benefit
            )
        except Exception as e:
            logger.error(f"SSIS VM实例解析错误: {e}")
            return None
    
    def parse_mysql_db_instance(self, row, tier: str, series: str = None) -> Optional[DatabaseInstance]:
        """解析MySQL数据库实例"""
        try:
            cells = row.find_all('td')
            if len(cells) < 4:
                return None
                
            name = cells[0].get_text(strip=True)
            vcores = int(cells[1].get_text(strip=True))
            memory = cells[2].get_text(strip=True)
            
            price = self.extract_price_from_text(cells[3].get_text())
            if not price:
                return None
                
            return DatabaseInstance(
                name=name,
                vcores=vcores,
                memory=memory,
                tier=tier,
                series=series,
                price=price
            )
        except Exception as e:
            logger.error(f"MySQL数据库实例解析错误: {e}")
            return None
    
    def extract_service_pricing(self, soup: BeautifulSoup, table_id: str, service_type: str) -> List[ServicePricing]:
        """提取服务定价（存储、IOPS、备份等）"""
        services = []
        try:
            table = soup.find('table', {'id': table_id.replace('#', '')})
            if not table:
                return services
            
            tbody = table.find('tbody')
            if not tbody:
                return services
                
            for row in tbody.find_all('tr'):
                cells = row.find_all('td')
                if len(cells) >= 2:
                    service_name = cells[0].get_text(strip=True)
                    price = cells[1].get_text(strip=True)
                    description = cells[2].get_text(strip=True) if len(cells) > 2 else None
                    
                    # 确定单位
                    unit = self._determine_unit(service_name, price)
                    
                    services.append(ServicePricing(
                        service_type=service_type,
                        price=price,
                        unit=unit,
                        description=description,
                        region_specific=self._is_region_specific_table(table_id)
                    ))
                    
        except Exception as e:
            logger.error(f"服务定价解析错误 {table_id}: {e}")
            
        return services
    
    def _determine_unit(self, service_name: str, price: str) -> str:
        """确定服务的计费单位"""
        combined_text = f"{service_name} {price}".lower()
        
        if "gb/月" in combined_text:
            return "GB/月"
        elif "iops/月" in combined_text:
            return "IOPS/月"
        elif "百万 iops" in combined_text:
            return "百万IOPS"
        elif "/小时" in combined_text:
            return "小时"
        else:
            return "未知"
    
    def _is_region_specific_table(self, table_id: str) -> bool:
        """判断是否为区域特定表格"""
        region_specific_patterns = [
            "_IOPS_East3", "_IOPS", "区域差异", "east3", "north3",
            "Azure_Database_For_MySQL7", "Azure_Database_For_MySQL8",
            "Azure_Database_For_MySQL20", "Azure_Database_For_MySQL18"
        ]
        return any(pattern in table_id for pattern in region_specific_patterns)
    
    def extract_product_info(self, soup: BeautifulSoup) -> ProductInfo:
        """提取产品基本信息"""
        try:
            # 产品名称
            title_element = soup.find('h2')
            if not title_element:
                title_element = soup.find('title')
            
            name = ""
            english_name = ""
            
            if title_element:
                title_text = title_element.get_text(strip=True)
                # 尝试分离中英文名称
                span_element = title_element.find('span')
                if span_element:
                    english_name = span_element.get_text(strip=True)
                    name = title_text.replace(english_name, "").strip()
                else:
                    name = title_text
            
            # 产品描述
            h4_element = soup.find('h4')
            description = h4_element.get_text(strip=True) if h4_element else ""
            
            # 图标路径
            icon_element = soup.find('img', src=True)
            icon_path = icon_element['src'] if icon_element else ""
            
            # Banner配置
            banner_element = soup.find('div', class_='common-banner')
            banner_config = {}
            if banner_element and banner_element.get('data-config'):
                try:
                    banner_config = json.loads(banner_element['data-config'])
                except:
                    banner_config = {}
            
            return ProductInfo(
                name=name,
                english_name=english_name,
                description=description,
                icon_path=icon_path,
                banner_config=banner_config
            )
        except Exception as e:
            logger.error(f"产品信息提取错误: {e}")
            return ProductInfo("", "", "", "", "", {})
    
    def discover_regions_from_html(self, soup: BeautifulSoup) -> List[str]:
        """从HTML中发现所有可用的区域"""
        regions = []
        
        try:
            # 查找区域选择器
            region_options = soup.find_all('option', {'data-href': True})
            if not region_options:
                region_options = soup.find_all('a', {'data-href': True})
            
            for option in region_options:
                region_id = option.get('value') or option.get('id', '')
                if region_id and region_id.startswith(('north-', 'east-')):
                    regions.append(region_id)
            
            # 如果没有找到，使用默认区域列表
            if not regions:
                regions = ['north-china3', 'east-china2', 'north-china2', 'east-china', 'north-china']
                logger.warning("未能从HTML中解析区域，使用默认区域列表")
            else:
                logger.info(f"从HTML中发现 {len(regions)} 个区域: {', '.join(regions)}")
        
        except Exception as e:
            logger.error(f"区域发现错误: {e}")
            regions = ['north-china3', 'east-china2', 'north-china2', 'east-china', 'north-china']
        
        return regions
    
    def discover_all_tables(self, soup: BeautifulSoup, product_type: str) -> Dict[str, PricingTable]:
        """发现HTML中所有的定价表格"""
        all_tables = {}
        
        # 查找所有表格
        tables = soup.find_all('table', {'id': True})
        
        for table in tables:
            table_id = table.get('id')
            full_table_id = f"#{table_id}"
            
            # 根据产品类型选择解析方法
            if product_type == 'ssis' and 'data-factory-ssis' in table_id:
                pricing_table = self.extract_ssis_pricing_table(soup, full_table_id)
            elif product_type == 'mysql' and ('MySQL' in table_id or 'mysql' in table_id.lower()):
                pricing_table = self.extract_mysql_pricing_table(soup, full_table_id)
            else:
                pricing_table = self.extract_generic_pricing_table(soup, full_table_id)
            
            if pricing_table and (pricing_table.instances or pricing_table.services):
                all_tables[full_table_id] = pricing_table
                logger.info(f"✓ 发现表格: {full_table_id} - {pricing_table.title}")
        
        logger.info(f"总共发现 {len(all_tables)} 个有效的定价表格")
        return all_tables
    
    def extract_ssis_pricing_table(self, soup: BeautifulSoup, table_id: str) -> Optional[PricingTable]:
        """提取SSIS定价表格"""
        try:
            table = soup.find('table', {'id': table_id.replace('#', '')})
            if not table:
                return None
            
            # 查找表格标题
            title_element = table.find_previous('h3')
            title = title_element.get_text(strip=True) if title_element else ""
            
            # 解析系列和许可类型
            vm_series = self._extract_vm_series(title)
            license_type = "Enterprise" if "企业" in title else "Standard"
            
            # 解析表格行
            instances = []
            tbody = table.find('tbody')
            if tbody:
                rows = tbody.find_all('tr')[1:]  # 跳过表头
                for row in rows:
                    instance = self.parse_ssis_vm_instance(row)
                    if instance:
                        instance.vm_series = vm_series
                        instance.license_type = license_type
                        instances.append(instance)
            
            return PricingTable(
                table_id=table_id,
                title=title,
                category="compute",
                vm_series=vm_series,
                license_type=license_type,
                instances=instances,
                region_specific=self._is_region_specific_table(table_id)
            )
        except Exception as e:
            logger.error(f"SSIS表格解析错误 {table_id}: {e}")
            return None
    
    def extract_mysql_pricing_table(self, soup: BeautifulSoup, table_id: str) -> Optional[PricingTable]:
        """提取MySQL定价表格"""
        try:
            # 判断是计算资源还是服务定价
            if any(keyword in table_id for keyword in ['_IOPS', 'storage', 'backup']):
                return self.extract_mysql_service_table(soup, table_id)
            else:
                return self.extract_mysql_compute_table(soup, table_id)
        except Exception as e:
            logger.error(f"MySQL表格解析错误 {table_id}: {e}")
            return None
    
    def extract_mysql_compute_table(self, soup: BeautifulSoup, table_id: str) -> Optional[PricingTable]:
        """提取MySQL计算资源表格"""
        try:
            table = soup.find('table', {'id': table_id.replace('#', '')})
            if not table:
                return None
            
            # 查找表格标题和层级信息
            title_element = table.find_previous('h2')
            title = title_element.get_text(strip=True) if title_element else ""
            
            # 确定层级和系列
            tier, series = self._determine_mysql_tier_and_series(table_id, title)
            
            # 解析表格行
            instances = []
            tbody = table.find('tbody')
            if tbody:
                rows = tbody.find_all('tr')[1:]  # 跳过表头
                for row in rows:
                    instance = self.parse_mysql_db_instance(row, tier, series)
                    if instance:
                        instances.append(instance)
            
            return PricingTable(
                table_id=table_id,
                title=title,
                category="compute",
                tier=tier,
                vm_series=series,
                instances=instances,
                region_specific=self._is_region_specific_table(table_id)
            )
        except Exception as e:
            logger.error(f"MySQL计算表格解析错误 {table_id}: {e}")
            return None
    
    def extract_mysql_service_table(self, soup: BeautifulSoup, table_id: str) -> Optional[PricingTable]:
        """提取MySQL服务定价表格"""
        try:
            # 确定服务类型
            service_type = "storage"
            if "_IOPS" in table_id:
                service_type = "iops"
            elif "backup" in table_id.lower():
                service_type = "backup"
            
            services = self.extract_service_pricing(soup, table_id, service_type)
            if not services:
                return None
            
            return PricingTable(
                table_id=table_id,
                title=f"{service_type.upper()}服务定价",
                category=service_type,
                services=services,
                region_specific=self._is_region_specific_table(table_id)
            )
        except Exception as e:
            logger.error(f"MySQL服务表格解析错误 {table_id}: {e}")
            return None
    
    def extract_generic_pricing_table(self, soup: BeautifulSoup, table_id: str) -> Optional[PricingTable]:
        """提取通用定价表格"""
        try:
            table = soup.find('table', {'id': table_id.replace('#', '')})
            if not table:
                return None
            
            title_element = table.find_previous(['h2', 'h3'])
            title = title_element.get_text(strip=True) if title_element else "通用定价表格"
            
            return PricingTable(
                table_id=table_id,
                title=title,
                category="generic",
                region_specific=self._is_region_specific_table(table_id)
            )
        except Exception as e:
            logger.error(f"通用表格解析错误 {table_id}: {e}")
            return None
    
    def _extract_vm_series(self, title: str) -> str:
        """从标题中提取VM系列"""
        series_patterns = [
            r'(Av2)', r'(Dv2)', r'(Dv3)', r'(Ev3)', r'(E.*v4)', r'(B系列)', r'(D系列)', r'(E系列)'
        ]
        for pattern in series_patterns:
            match = re.search(pattern, title)
            if match:
                return match.group(1)
        return "Unknown"
    
    def _determine_mysql_tier_and_series(self, table_id: str, title: str) -> tuple:
        """确定MySQL的层级和系列"""
        tier = "通用"
        series = "Unknown"
        
        if "Azure_Database_For_MySQL5" in table_id:
            tier, series = "可突增", "B系列"
        elif "Azure_Database_For_MySQL6" in table_id or "Azure_Database_For_MySQL7" in table_id:
            tier, series = "常规用途", "D系列"
        elif "Azure_Database_For_MySQL4" in table_id or "Azure_Database_For_MySQL8" in table_id:
            tier, series = "业务关键", "E系列"
        
        return tier, series
    
    def load_region_configuration(self, config_path: str, product_name: str) -> Dict[str, List[str]]:
        """加载区域配置文件"""
        region_exclusion_mapping = {}
        
        try:
            if not os.path.exists(config_path):
                logger.warning(f"配置文件不存在: {config_path}")
                return region_exclusion_mapping
                
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 查找匹配的产品配置
            for item in config:
                item_os = item.get('os', '')
                if any(keyword in item_os for keyword in [product_name, 'SSIS', 'MySQL']):
                    region_id = item.get('region', '')
                    excluded_table_ids = item.get('tableIDs', [])
                    region_exclusion_mapping[region_id] = excluded_table_ids
            
            logger.info(f"加载了 {len(region_exclusion_mapping)} 个区域的配置")
                    
        except Exception as e:
            logger.error(f"区域配置加载错误: {e}")
        
        return region_exclusion_mapping
    
    def generate_business_insights(self, regional_data: List[RegionConfig], all_tables: Dict[str, PricingTable]) -> BusinessInsights:
        """生成业务洞察"""
        insights = BusinessInsights()
        
        # 分析完整服务区域和成本优化区域
        for region in regional_data:
            table_count = len(region.available_tables)
            if table_count >= 8:
                insights.full_feature_regions.append(region.region_name)
            
            if "成本优化" in region.config_status or "优化" in region.region_name:
                insights.cost_optimization_regions.append(region.region_name)
        
        # 生成推荐
        if insights.full_feature_regions:
            insights.recommendations.append(f"推荐完整功能区域: {', '.join(insights.full_feature_regions)}")
        
        if insights.cost_optimization_regions:
            insights.recommendations.append(f"推荐成本优化区域: {', '.join(insights.cost_optimization_regions)}")
        
        insights.recommendations.extend([
            "开发测试环境优先选择成本优化区域",
            "生产环境考虑完整功能区域的稳定性",
            "定期评估区域间的价格差异和功能更新"
        ])
        
        return insights
    
    def extract_complete_product_analysis(self, html_content: str, config_path: str = None) -> AzureProductAnalysis:
        """提取完整的产品分析"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        logger.info("=== 开始Azure产品定价数据提取 ===")
        
        # 1. 检测产品类型
        product_type = self.detect_product_type(soup, html_content)
        
        # 2. 提取产品基本信息
        product_info = self.extract_product_info(soup)
        
        # 3. 发现所有区域
        all_regions = self.discover_regions_from_html(soup)
        
        # 4. 发现所有表格
        all_tables = self.discover_all_tables(soup, product_type)
        
        # 5. 加载区域配置
        region_exclusions = {}
        if config_path and os.path.exists(config_path):
            region_exclusions = self.load_region_configuration(config_path, product_info.name)
        
        # 6. 为每个区域生成配置
        regions = []
        all_table_ids = list(all_tables.keys())
        
        for region_id in all_regions:
            region_name = self.region_mapping.get(region_id, region_id)
            
            # 确定配置状态和排除列表
            if region_id in region_exclusions:
                excluded_table_ids = region_exclusions[region_id]
                if len(excluded_table_ids) == 0:
                    config_status = "✅ 配置为空数组"
                else:
                    config_status = f"⚠️ 配置排除 {len(excluded_table_ids)} 个表格"
            else:
                excluded_table_ids = []
                config_status = "✅ 无配置（默认全部显示）"
            
            # 计算可用表格
            available_table_ids = [tid for tid in all_table_ids if tid not in excluded_table_ids]
            available_tables = [all_tables[tid] for tid in available_table_ids if tid in all_tables]
            
            # 特殊功能分析
            special_features = []
            if region_id in ['north-china3', 'east-china3']:
                special_features.append("新一代区域")
            if any("IOPS" in table.table_id for table in available_tables):
                special_features.append("IO优化支持")
            if len(available_tables) >= 10:
                special_features.append("完整服务支持")
            
            regions.append(RegionConfig(
                region_id=region_id,
                region_name=region_name,
                is_default=(region_id == 'north-china3'),
                excluded_tables=excluded_table_ids,
                available_tables=available_tables,
                special_features=special_features,
                config_status=config_status
            ))
            
            logger.info(f"📍 {region_name}: {len(available_tables)}个可用表格")
        
        # 7. 生成业务洞察
        business_insights = self.generate_business_insights(regions, all_tables)
        
        # 8. 生成元数据
        metadata = {
            'total_tables': len(all_tables),
            'total_regions': len(regions),
            'available_regions': len([r for r in regions if r.available_tables]),
            'product_type': product_type,
            'extraction_method': 'html_parsing'
        }
        
        return AzureProductAnalysis(
            product_info=product_info,
            product_type=product_type,
            regions=regions,
            pricing_tables=list(all_tables.values()),
            business_insights=business_insights,
            extraction_timestamp=datetime.now().isoformat(),
            metadata=metadata
        )

# ================================
# 数据导出器
# ================================

class AzureDataExporter:
    """Azure数据导出器"""
    
    @staticmethod
    def export_to_json(analysis: AzureProductAnalysis, file_path: str):
        """导出为JSON格式"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(asdict(analysis), f, ensure_ascii=False, indent=2)
            logger.info(f"JSON导出成功: {file_path}")
        except Exception as e:
            logger.error(f"JSON导出失败: {e}")
    
    @staticmethod
    def export_to_excel(analysis: AzureProductAnalysis, file_path: str):
        """导出为Excel格式"""
        try:
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                
                # 产品概览
                overview_data = [{
                    'product_name': analysis.product_info.name,
                    'product_type': analysis.product_type,
                    'description': analysis.product_info.description,
                    'total_regions': len(analysis.regions),
                    'total_tables': len(analysis.pricing_tables),
                    'extraction_timestamp': analysis.extraction_timestamp
                }]
                overview_df = pd.DataFrame(overview_data)
                overview_df.to_excel(writer, sheet_name='产品概览', index=False)
                
                # 区域概览
                region_data = []
                for region in analysis.regions:
                    region_data.append({
                        'region_id': region.region_id,
                        'region_name': region.region_name,
                        'is_default': region.is_default,
                        'config_status': region.config_status,
                        'excluded_tables_count': len(region.excluded_tables),
                        'available_tables_count': len(region.available_tables),
                        'special_features': '; '.join(region.special_features)
                    })
                
                region_df = pd.DataFrame(region_data)
                region_df.to_excel(writer, sheet_name='区域概览', index=False)
                
                # 计算资源定价（如果有）
                compute_data = []
                for region in analysis.regions:
                    for table in region.available_tables:
                        if table.category == "compute" and table.instances:
                            for instance in table.instances:
                                if isinstance(instance, VMInstance):
                                    # SSIS VM实例
                                    row = {
                                        'region': region.region_name,
                                        'table_id': table.table_id,
                                        'vm_series': instance.vm_series,
                                        'license_type': instance.license_type,
                                        'instance_name': instance.name,
                                        'vcores': instance.vcores,
                                        'memory_gb': instance.memory_gb,
                                        'temp_storage': instance.temp_storage,
                                        'hourly_price': instance.license_included_price.hourly_price,
                                        'monthly_price': instance.license_included_price.monthly_price,
                                        'hybrid_benefit_hourly': instance.hybrid_benefit.price.hourly_price if instance.hybrid_benefit else None,
                                        'hybrid_savings_pct': instance.hybrid_benefit.savings_percentage if instance.hybrid_benefit else None
                                    }
                                elif isinstance(instance, DatabaseInstance):
                                    # MySQL数据库实例
                                    row = {
                                        'region': region.region_name,
                                        'table_id': table.table_id,
                                        'tier': instance.tier,
                                        'series': instance.series,
                                        'instance_name': instance.name,
                                        'vcores': instance.vcores,
                                        'memory': instance.memory,
                                        'hourly_price': instance.price.hourly_price if instance.price else None,
                                        'monthly_price': instance.price.monthly_price if instance.price else None
                                    }
                                compute_data.append(row)
                
                if compute_data:
                    compute_df = pd.DataFrame(compute_data)
                    compute_df.to_excel(writer, sheet_name='计算资源定价', index=False)
                
                # 服务定价（如果有）
                service_data = []
                for table in analysis.pricing_tables:
                    if table.services:
                        for service in table.services:
                            service_data.append({
                                'table_id': table.table_id,
                                'service_type': service.service_type,
                                'price': service.price,
                                'unit': service.unit,
                                'description': service.description,
                                'region_specific': service.region_specific
                            })
                
                if service_data:
                    service_df = pd.DataFrame(service_data)
                    service_df.to_excel(writer, sheet_name='服务定价', index=False)
                
                # 业务建议
                recommendation_data = [{'recommendation': rec} for rec in analysis.business_insights.recommendations]
                if recommendation_data:
                    rec_df = pd.DataFrame(recommendation_data)
                    rec_df.to_excel(writer, sheet_name='业务建议', index=False)
            
            logger.info(f"Excel导出成功: {file_path}")
        except Exception as e:
            logger.error(f"Excel导出失败: {e}")
    
    @staticmethod
    def generate_summary_report(analysis: AzureProductAnalysis) -> str:
        """生成摘要报告"""
        report = f"""
# {analysis.product_info.name} 定价分析报告

## 产品概览
- **产品名称**: {analysis.product_info.name}
- **产品类型**: {analysis.product_type}
- **产品描述**: {analysis.product_info.description}
- **分析时间**: {analysis.extraction_timestamp}

## 数据统计
- **总区域数**: {len(analysis.regions)}
- **总表格数**: {len(analysis.pricing_tables)}
- **可用区域数**: {len([r for r in analysis.regions if r.available_tables])}

## 区域分析
"""
        
        for region in analysis.regions:
            status = "✅ 可用" if region.available_tables else "❌ 不可用"
            report += f"""
### {region.region_name}
- **状态**: {status}
- **配置**: {region.config_status}
- **可用表格**: {len(region.available_tables)}个
- **特殊功能**: {', '.join(region.special_features) if region.special_features else '无'}
"""
        
        report += f"""
## 业务建议
"""
        for rec in analysis.business_insights.recommendations:
            report += f"- {rec}\n"
        
        return report

# ================================
# 批量处理器
# ================================

class AzureBatchProcessor:
    """Azure产品批量处理器"""
    
    def __init__(self, extractor: AzureProductPricingExtractor, exporter: AzureDataExporter):
        self.extractor = extractor
        self.exporter = exporter
    
    def process_directory(self, input_dir: str, output_dir: str, config_path: str = None):
        """批量处理目录中的HTML文件"""
        html_files = glob.glob(os.path.join(input_dir, "*.html"))
        
        if not html_files:
            logger.warning(f"在目录 {input_dir} 中未找到HTML文件")
            return
        
        os.makedirs(output_dir, exist_ok=True)
        
        results = []
        
        for html_file in html_files:
            try:
                logger.info(f"处理文件: {html_file}")
                
                with open(html_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # 提取分析
                analysis = self.extractor.extract_complete_product_analysis(html_content, config_path)
                
                # 生成输出文件名
                base_name = os.path.splitext(os.path.basename(html_file))[0]
                
                # 导出JSON
                json_path = os.path.join(output_dir, f"{base_name}_analysis.json")
                self.exporter.export_to_json(analysis, json_path)
                
                # 导出Excel
                excel_path = os.path.join(output_dir, f"{base_name}_analysis.xlsx")
                self.exporter.export_to_excel(analysis, excel_path)
                
                # 生成报告
                report = self.exporter.generate_summary_report(analysis)
                report_path = os.path.join(output_dir, f"{base_name}_report.md")
                with open(report_path, 'w', encoding='utf-8') as f:
                    f.write(report)
                
                results.append({
                    'file': html_file,
                    'product': analysis.product_info.name,
                    'type': analysis.product_type,
                    'regions': len(analysis.regions),
                    'tables': len(analysis.pricing_tables),
                    'status': 'success'
                })
                
            except Exception as e:
                logger.error(f"处理文件 {html_file} 失败: {e}")
                results.append({
                    'file': html_file,
                    'status': 'failed',
                    'error': str(e)
                })
        
        # 生成批量处理报告
        self._generate_batch_report(results, output_dir)
    
    def _generate_batch_report(self, results: List[Dict], output_dir: str):
        """生成批量处理报告"""
        try:
            report_path = os.path.join(output_dir, "batch_processing_report.json")
            
            summary = {
                'total_files': len(results),
                'successful': len([r for r in results if r.get('status') == 'success']),
                'failed': len([r for r in results if r.get('status') == 'failed']),
                'timestamp': datetime.now().isoformat(),
                'results': results
            }
            
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            logger.info(f"批量处理完成，报告保存至: {report_path}")
            
        except Exception as e:
            logger.error(f"生成批量处理报告失败: {e}")

# ================================
# 主程序和使用示例
# ================================

def main():
    """主程序示例"""
    # 初始化
    extractor = AzureProductPricingExtractor()
    exporter = AzureDataExporter()
    batch_processor = AzureBatchProcessor(extractor, exporter)
    
    # 示例1: 处理单个SSIS文件
    try:
        logger.info("=== 处理SSIS产品 ===")
        with open('ssis.html', 'r', encoding='utf-8') as f:
            ssis_content = f.read()
        
        ssis_analysis = extractor.extract_complete_product_analysis(ssis_content, 'soft-category.json')
        
        # 导出结果
        exporter.export_to_json(ssis_analysis, 'ssis_pricing_analysis.json')
        exporter.export_to_excel(ssis_analysis, 'ssis_pricing_analysis.xlsx')
        
        # 生成报告
        ssis_report = exporter.generate_summary_report(ssis_analysis)
        with open('ssis_report.md', 'w', encoding='utf-8') as f:
            f.write(ssis_report)
        
        logger.info(f"SSIS分析完成: {ssis_analysis.product_info.name}")
        
    except FileNotFoundError:
        logger.warning("ssis.html文件未找到，跳过SSIS处理")
    except Exception as e:
        logger.error(f"SSIS处理失败: {e}")
    
    # 示例2: 处理单个MySQL文件
    try:
        logger.info("=== 处理MySQL产品 ===")
        with open('mysql-index.html', 'r', encoding='utf-8') as f:
            mysql_content = f.read()
        
        mysql_analysis = extractor.extract_complete_product_analysis(mysql_content, 'soft-category.json')
        
        # 导出结果
        exporter.export_to_json(mysql_analysis, 'mysql_pricing_analysis.json')
        exporter.export_to_excel(mysql_analysis, 'mysql_pricing_analysis.xlsx')
        
        # 生成报告
        mysql_report = exporter.generate_summary_report(mysql_analysis)
        with open('mysql_report.md', 'w', encoding='utf-8') as f:
            f.write(mysql_report)
        
        logger.info(f"MySQL分析完成: {mysql_analysis.product_info.name}")
        
    except FileNotFoundError:
        logger.warning("index.html文件未找到，跳过MySQL处理")
    except Exception as e:
        logger.error(f"MySQL处理失败: {e}")
    
    # 示例3: 批量处理
    try:
        logger.info("=== 批量处理示例 ===")
        # batch_processor.process_directory('input_htmls', 'output_analysis', 'soft-category.json')
        logger.info("批量处理功能已准备就绪，请提供输入目录路径")
    except Exception as e:
        logger.error(f"批量处理失败: {e}")

if __name__ == "__main__":
    main()