#!/usr/bin/env python3
"""
AI增强的自适应HTML解析器
"""

import json
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from bs4 import BeautifulSoup
import pandas as pd

@dataclass
class TableStructureAnalysis:
    """表格结构分析结果"""
    table_id: str
    column_count: int
    row_count: int
    header_row: List[str]
    sample_rows: List[List[str]]
    detected_patterns: Dict[str, Any]
    confidence_score: float

@dataclass
class AutoDetectedSchema:
    """自动检测的schema"""
    table_id: str
    column_types: Dict[int, str]  # 列索引 -> 数据类型
    price_columns: List[int]
    name_columns: List[int]
    spec_columns: List[int]
    confidence: float

class AITableAnalyzer:
    """AI表格分析器"""
    
    def __init__(self):
        self.price_patterns = [
            r'￥\s*[\d,\.]+',
            r'\$\s*[\d,\.]+',
            r'[\d,\.]+\s*元',
            r'[\d,\.]+/小时',
            r'[\d,\.]+/月'
        ]
        
        self.spec_patterns = [
            r'\d+\s*vCore',
            r'\d+\s*GB',
            r'\d+\s*TB',
            r'\d+\s*IOPS',
            r'\d+\s*核',
            r'\d+\s*内存'
        ]
        
        self.name_indicators = [
            '实例', '型号', '规格', '配置', 'instance', 'type', 'size'
        ]
    
    def analyze_table_structure(self, html_table) -> TableStructureAnalysis:
        """分析表格结构"""
        table_id = html_table.get('id', 'unknown')
        
        # 获取所有行
        rows = html_table.find_all('tr')
        
        # 分析表头
        header_row = []
        if rows:
            header_cells = rows[0].find_all(['th', 'td'])
            header_row = [cell.get_text(strip=True) for cell in header_cells]
        
        # 获取样本数据行
        sample_rows = []
        data_rows = rows[1:] if len(rows) > 1 else []
        
        for row in data_rows[:5]:  # 取前5行作为样本
            cells = row.find_all(['td', 'th'])
            row_data = [cell.get_text(strip=True) for cell in cells]
            if row_data and not any(cell.find('strong') for cell in cells):
                sample_rows.append(row_data)
        
        # 检测模式
        detected_patterns = self._detect_patterns(header_row, sample_rows)
        
        # 计算置信度
        confidence = self._calculate_confidence(detected_patterns, sample_rows)
        
        return TableStructureAnalysis(
            table_id=table_id,
            column_count=len(header_row),
            row_count=len(data_rows),
            header_row=header_row,
            sample_rows=sample_rows,
            detected_patterns=detected_patterns,
            confidence_score=confidence
        )
    
    def _detect_patterns(self, headers: List[str], sample_rows: List[List[str]]) -> Dict[str, Any]:
        """检测数据模式"""
        patterns = {
            'price_columns': [],
            'name_columns': [],
            'spec_columns': [],
            'column_types': {}
        }
        
        if not sample_rows:
            return patterns
        
        # 分析每一列
        for col_idx in range(len(headers)):
            header = headers[col_idx] if col_idx < len(headers) else ""
            
            # 收集该列的所有值
            column_values = []
            for row in sample_rows:
                if col_idx < len(row):
                    column_values.append(row[col_idx])
            
            # 分析列类型
            column_type = self._analyze_column_type(header, column_values)
            patterns['column_types'][col_idx] = column_type
            
            # 分类列
            if column_type == 'price':
                patterns['price_columns'].append(col_idx)
            elif column_type == 'name':
                patterns['name_columns'].append(col_idx)
            elif column_type == 'spec':
                patterns['spec_columns'].append(col_idx)
        
        return patterns
    
    def _analyze_column_type(self, header: str, values: List[str]) -> str:
        """分析列类型"""
        header_lower = header.lower()
        
        # 检查价格列
        price_indicators = ['价格', 'price', '费用', '成本', '￥', '$', '元']
        if any(indicator in header_lower for indicator in price_indicators):
            return 'price'
        
        # 检查值中的价格模式
        price_count = 0
        for value in values:
            if any(re.search(pattern, value) for pattern in self.price_patterns):
                price_count += 1
        
        if price_count > len(values) * 0.7:  # 70%以上包含价格
            return 'price'
        
        # 检查名称列
        name_indicators = ['实例', '型号', '规格', '名称', 'instance', 'name', 'type']
        if any(indicator in header_lower for indicator in name_indicators):
            return 'name'
        
        # 检查规格列
        spec_indicators = ['vcore', '内存', 'memory', '存储', 'storage', 'cpu', '核']
        if any(indicator in header_lower for indicator in spec_indicators):
            return 'spec'
        
        # 检查值中的规格模式
        spec_count = 0
        for value in values:
            if any(re.search(pattern, value) for pattern in self.spec_patterns):
                spec_count += 1
        
        if spec_count > len(values) * 0.5:  # 50%以上包含规格
            return 'spec'
        
        # 检查是否为数字
        number_count = 0
        for value in values:
            try:
                float(value.replace(',', ''))
                number_count += 1
            except:
                pass
        
        if number_count > len(values) * 0.8:  # 80%以上为数字
            return 'number'
        
        return 'text'
    
    def _calculate_confidence(self, patterns: Dict[str, Any], sample_rows: List[List[str]]) -> float:
        """计算置信度"""
        if not sample_rows:
            return 0.0
        
        confidence_factors = []
        
        # 价格列检测置信度
        if patterns['price_columns']:
            confidence_factors.append(0.8)
        else:
            confidence_factors.append(0.2)
        
        # 名称列检测置信度
        if patterns['name_columns']:
            confidence_factors.append(0.7)
        else:
            confidence_factors.append(0.3)
        
        # 数据完整性
        complete_rows = sum(1 for row in sample_rows if all(cell.strip() for cell in row))
        completeness = complete_rows / len(sample_rows) if sample_rows else 0
        confidence_factors.append(completeness)
        
        return sum(confidence_factors) / len(confidence_factors)
    
    def generate_auto_schema(self, analysis: TableStructureAnalysis) -> AutoDetectedSchema:
        """生成自动检测的schema"""
        patterns = analysis.detected_patterns
        
        return AutoDetectedSchema(
            table_id=analysis.table_id,
            column_types=patterns['column_types'],
            price_columns=patterns['price_columns'],
            name_columns=patterns['name_columns'],
            spec_columns=patterns['spec_columns'],
            confidence=analysis.confidence_score
        )

class AdaptiveExtractor:
    """自适应提取器"""
    
    def __init__(self):
        self.analyzer = AITableAnalyzer()
        self.extraction_cache = {}
    
    def extract_unknown_product(self, html_content: str, product_name: str) -> Dict[str, Any]:
        """提取未知产品的数据"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 发现所有表格
        tables = soup.find_all('table', {'id': True})
        
        extracted_tables = []
        analysis_results = []
        
        for table in tables:
            # 分析表格结构
            analysis = self.analyzer.analyze_table_structure(table)
            analysis_results.append(analysis)
            
            # 如果置信度足够高，尝试提取
            if analysis.confidence_score > 0.5:
                auto_schema = self.analyzer.generate_auto_schema(analysis)
                extracted_table = self._extract_with_auto_schema(table, auto_schema, soup)
                
                if extracted_table:
                    extracted_tables.append(extracted_table)
        
        return {
            'product_name': product_name,
            'extraction_method': 'adaptive',
            'tables': extracted_tables,
            'analysis_results': [asdict(analysis) for analysis in analysis_results],
            'total_tables_found': len(tables),
            'successfully_extracted': len(extracted_tables)
        }
    
    def _extract_with_auto_schema(self, html_table, auto_schema: AutoDetectedSchema, soup: BeautifulSoup) -> Optional[Dict[str, Any]]:
        """使用自动schema提取表格"""
        try:
            # 提取标题
            title = self._extract_title(html_table, soup)
            
            # 提取实例
            instances = self._extract_instances_adaptive(html_table, auto_schema)
            
            if not instances:
                return None
            
            return {
                'table_id': auto_schema.table_id,
                'title': title,
                'category': self._guess_category(title),
                'service_tier': 'auto-detected',
                'instances': instances,
                'extraction_metadata': {
                    'confidence': auto_schema.confidence,
                    'column_types': auto_schema.column_types,
                    'extraction_method': 'adaptive'
                }
            }
            
        except Exception as e:
            print(f"自适应提取错误 {auto_schema.table_id}: {e}")
            return None
    
    def _extract_title(self, html_table, soup: BeautifulSoup) -> str:
        """提取表格标题"""
        # 尝试多种方式查找标题
        selectors = ['h1', 'h2', 'h3', 'h4', 'h5']
        
        for selector in selectors:
            title_element = html_table.find_previous(selector)
            if title_element:
                return title_element.get_text(strip=True)
        
        return f"表格_{html_table.get('id', 'unknown')}"
    
    def _guess_category(self, title: str) -> str:
        """猜测类别"""
        title_lower = title.lower()
        
        category_keywords = {
            '计算': ['计算', 'compute', 'vm', '虚拟机', 'instance'],
            '存储': ['存储', 'storage', 'disk', '磁盘'],
            '网络': ['网络', 'network', 'bandwidth', '带宽'],
            '数据库': ['数据库', 'database', 'db', 'mysql', 'sql'],
            '分析': ['分析', 'analytics', 'data', '数据'],
            'AI': ['ai', '人工智能', 'machine learning', 'ml', '机器学习']
        }
        
        for category, keywords in category_keywords.items():
            if any(keyword in title_lower for keyword in keywords):
                return category
        
        return '其他'
    
    def _extract_instances_adaptive(self, html_table, auto_schema: AutoDetectedSchema) -> List[Dict[str, Any]]:
        """自适应提取实例"""
        instances = []
        
        # 获取数据行
        tbody = html_table.find('tbody')
        if not tbody:
            rows = html_table.find_all('tr')[1:]
        else:
            rows = tbody.find_all('tr')
        
        for row in rows:
            instance = self._extract_instance_adaptive(row, auto_schema)
            if instance:
                instances.append(instance)
        
        return instances
    
    def _extract_instance_adaptive(self, row, auto_schema: AutoDetectedSchema) -> Optional[Dict[str, Any]]:
        """自适应提取实例"""
        try:
            cells = row.find_all(['td', 'th'])
            if len(cells) < 2:
                return None
            
            # 跳过表头行
            if any(cell.find('strong') for cell in cells):
                return None
            
            instance_data = {
                'name': '',
                'price': {},
                'attributes': {}
            }
            
            # 提取名称
            if auto_schema.name_columns:
                name_col = auto_schema.name_columns[0]
                if name_col < len(cells):
                    instance_data['name'] = cells[name_col].get_text(strip=True)
            
            # 如果没有检测到名称列，使用第一列
            if not instance_data['name'] and cells:
                instance_data['name'] = cells[0].get_text(strip=True)
            
            # 提取价格
            for price_col in auto_schema.price_columns:
                if price_col < len(cells):
                    price_text = cells[price_col].get_text(strip=True)
                    price_data = self._extract_price_adaptive(price_text)
                    if price_data:
                        instance_data['price'].update(price_data)
            
            # 提取其他属性
            for col_idx, cell in enumerate(cells):
                if col_idx not in auto_schema.price_columns and col_idx not in auto_schema.name_columns:
                    column_type = auto_schema.column_types.get(col_idx, 'text')
                    value = cell.get_text(strip=True)
                    
                    if value:
                        instance_data['attributes'][f'column_{col_idx}'] = {
                            'value': value,
                            'type': column_type
                        }
            
            # 验证实例数据
            if not instance_data['name'] or not instance_data['price']:
                return None
            
            return instance_data
            
        except Exception as e:
            print(f"自适应实例提取错误: {e}")
            return None
    
    def _extract_price_adaptive(self, price_text: str) -> Optional[Dict[str, Any]]:
        """自适应价格提取"""
        # 价格模式
        patterns = [
            (r'￥\s*([\d,\.]+)/小时', 'hourly'),
            (r'￥\s*([\d,\.]+)/月', 'monthly'),
            (r'每百万\s*IOPS\s*￥([\d,\.]+)', 'iops'),
            (r'￥\s*([\d,\.]+)', 'unit')
        ]
        
        for pattern, price_type in patterns:
            match = re.search(pattern, price_text)
            if match:
                price = float(match.group(1).replace(',', ''))
                
                result = {
                    'currency': 'CNY',
                    'raw_text': price_text
                }
                
                if price_type == 'hourly':
                    result['hourly_price'] = price
                    result['monthly_price'] = price * 24 * 31
                    result['pricing_model'] = '现用现付'
                elif price_type == 'monthly':
                    result['monthly_price'] = price
                    result['pricing_model'] = '包月'
                elif price_type == 'iops':
                    result['iops_price'] = price
                    result['pricing_model'] = '按IOPS计费'
                else:
                    result['unit_price'] = price
                    result['pricing_model'] = '其他'
                
                return result
        
        return None

# 使用示例
def main():
    """自适应提取器使用示例"""
    extractor = AdaptiveExtractor()
    
    # 测试未知产品
    with open('mysql-index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    result = extractor.extract_unknown_product(html_content, "Unknown MySQL Product")
    
    print(f"发现表格: {result['total_tables_found']}个")
    print(f"成功提取: {result['successfully_extracted']}个")
    
    # 输出分析结果
    for analysis in result['analysis_results']:
        print(f"\n表格 {analysis['table_id']}:")
        print(f"  置信度: {analysis['confidence_score']:.2f}")
        print(f"  列数: {analysis['column_count']}")
        print(f"  行数: {analysis['row_count']}")
        print(f"  检测到的模式: {analysis['detected_patterns']}")

if __name__ == "__main__":
    main()
