import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from bs4 import BeautifulSoup
import pandas as pd

@dataclass
class PriceDetail:
    """价格详情"""
    hourly_price: float
    monthly_price: float
    currency: str = "CNY"

@dataclass
class HybridBenefit:
    """混合优惠信息"""
    price: PriceDetail
    savings_percentage: int

@dataclass
class VMInstance:
    """虚拟机实例规格"""
    name: str
    vcores: int
    memory_gb: float
    temp_storage: str
    license_included_price: PriceDetail
    hybrid_benefit: Optional[HybridBenefit] = None

@dataclass
class PricingTable:
    """定价表格"""
    table_id: str
    title: str
    vm_series: str
    license_type: str
    instances: List[VMInstance]

@dataclass
class RegionalPricingData:
    """区域定价数据"""
    region_id: str
    region_name: str
    config_status: str  # 配置状态：无配置/空数组/有排除
    excluded_tables: List[str]
    available_tables: List[PricingTable]
    total_available_configurations: int
    service_level: str

@dataclass
class CompleteRegionalAnalysis:
    """完整的区域分析结果"""
    product_name: str
    extraction_timestamp: str
    total_regions: int
    full_service_regions: int
    partial_service_regions: int
    regional_data: List[RegionalPricingData]
    business_insights: Dict[str, Any]

class AzureRegionalPricingExtractor:
    """Azure区域定价数据提取器 - 最终正确版本"""
    
    def __init__(self):
        self.currency_pattern = r'￥([\d,\.]+)'
        self.percentage_pattern = r'（~(\d+)%）'
        
        # 区域映射表
        self.region_mapping = {
            'north-china': '中国北部',
            'north-china2': '中国北部 2', 
            'north-china3': '中国北部 3',
            'east-china': '中国东部',
            'east-china2': '中国东部 2',
            'east-china3': '中国东部 3'
        }
    
    def extract_price_from_text(self, price_text: str) -> Optional[PriceDetail]:
        """从价格文本中提取价格信息"""
        try:
            hourly_match = re.search(r'￥([\d,\.]+)/小时', price_text)
            if not hourly_match:
                return None
            hourly_price = float(hourly_match.group(1).replace(',', ''))
            
            monthly_match = re.search(r'约￥([\d,\.]+)/月', price_text)
            monthly_price = float(monthly_match.group(1).replace(',', '')) if monthly_match else hourly_price * 24 * 31
            
            return PriceDetail(
                hourly_price=hourly_price,
                monthly_price=monthly_price,
                currency="CNY"
            )
        except Exception as e:
            print(f"价格解析错误: {price_text}, 错误: {e}")
            return None
    
    def extract_hybrid_benefit(self, benefit_text: str) -> Optional[HybridBenefit]:
        """提取混合优惠信息"""
        try:
            price = self.extract_price_from_text(benefit_text)
            if not price:
                return None
                
            percentage_match = re.search(self.percentage_pattern, benefit_text)
            savings_percentage = int(percentage_match.group(1)) if percentage_match else 0
            
            return HybridBenefit(
                price=price,
                savings_percentage=savings_percentage
            )
        except Exception as e:
            print(f"混合优惠解析错误: {benefit_text}, 错误: {e}")
            return None
    
    def parse_vm_instance(self, row) -> Optional[VMInstance]:
        """解析虚拟机实例行"""
        try:
            cells = row.find_all('td')
            if len(cells) < 5:
                return None
                
            name = cells[0].get_text(strip=True)
            vcores = int(cells[1].get_text(strip=True))
            
            memory_text = cells[2].get_text(strip=True)
            memory_gb = float(re.search(r'([\d\.]+)', memory_text).group(1))
            
            temp_storage = cells[3].get_text(strip=True)
            
            license_price = self.extract_price_from_text(cells[4].get_text())
            if not license_price:
                return None
                
            hybrid_benefit = None
            if len(cells) > 5:
                hybrid_benefit = self.extract_hybrid_benefit(cells[5].get_text())
            
            return VMInstance(
                name=name,
                vcores=vcores,
                memory_gb=memory_gb,
                temp_storage=temp_storage,
                license_included_price=license_price,
                hybrid_benefit=hybrid_benefit
            )
        except Exception as e:
            print(f"VM实例解析错误: {e}")
            return None
    
    def extract_single_pricing_table(self, soup: BeautifulSoup, table_id: str) -> Optional[PricingTable]:
        """提取单个定价表格"""
        try:
            table = soup.find('table', {'id': table_id.replace('#', '')})
            if not table:
                return None
            
            # 查找表格标题
            title_element = table.find_previous('h3')
            title = title_element.get_text(strip=True) if title_element else ""
            
            # 解析系列和许可类型
            vm_series = self._extract_vm_series(title)
            license_type = "Enterprise" if "企业" in title else "Standard"
            
            # 解析表格行
            instances = []
            tbody = table.find('tbody')
            if tbody:
                rows = tbody.find_all('tr')[1:]  # 跳过表头
                for row in rows:
                    instance = self.parse_vm_instance(row)
                    if instance:
                        instances.append(instance)
            
            return PricingTable(
                table_id=table_id,
                title=title,
                vm_series=vm_series,
                license_type=license_type,
                instances=instances
            )
        except Exception as e:
            print(f"表格解析错误 {table_id}: {e}")
            return None
    
    def _extract_vm_series(self, title: str) -> str:
        """从标题中提取VM系列"""
        series_patterns = [
            r'(Av2)', r'(Dv2)', r'(Dv3)', r'(Ev3)', r'(E.*v4)'
        ]
        for pattern in series_patterns:
            match = re.search(pattern, title)
            if match:
                return match.group(1)
        return "Unknown"
    
    def discover_all_tables_in_html(self, soup: BeautifulSoup) -> Dict[str, PricingTable]:
        """发现HTML中所有的SSIS定价表格"""
        all_tables = {}
        
        # 查找所有可能的表格ID模式
        tables = soup.find_all('table', {'id': True})
        
        for table in tables:
            table_id = table.get('id')
            if 'data-factory-ssis' in table_id:
                full_table_id = f"#{table_id}"
                pricing_table = self.extract_single_pricing_table(soup, full_table_id)
                if pricing_table and pricing_table.instances:  # 只保留有数据的表格
                    all_tables[full_table_id] = pricing_table
                    print(f"✓ 发现表格: {full_table_id} - {pricing_table.title}")
        
        print(f"\n总共发现 {len(all_tables)} 个有效的SSIS定价表格")
        return all_tables
    
    def discover_regions_from_html(self, soup: BeautifulSoup) -> List[str]:
        """从HTML中发现所有可用的区域"""
        regions = []
        
        # 查找区域选择器
        region_options = soup.find_all('option', {'data-href': True})
        if not region_options:
            # 备选方法：查找链接
            region_options = soup.find_all('a', {'data-href': True})
        
        for option in region_options:
            region_id = option.get('id') or option.get('data-href', '').replace('#', '')
            if region_id and region_id.startswith(('north-', 'east-')):
                regions.append(region_id)
        
        # 如果没有找到，使用默认区域列表
        if not regions:
            regions = ['north-china3', 'east-china2', 'north-china2', 'east-china', 'north-china']
            print("⚠️ 未能从HTML中解析区域，使用默认区域列表")
        else:
            print(f"✓ 从HTML中发现 {len(regions)} 个区域: {', '.join(regions)}")
        
        return regions
    
    def load_region_configuration(self, config_path: str) -> Dict[str, List[str]]:
        """加载区域配置文件 - 获取要排除的表格列表"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            region_exclusion_mapping = {}
            for item in config:
                if item.get('os') == 'Data Factory SSIS':
                    region_id = item.get('region', '')
                    excluded_table_ids = item.get('tableIDs', [])  # 要排除的表格
                    region_exclusion_mapping[region_id] = excluded_table_ids
                    
            return region_exclusion_mapping
        except Exception as e:
            print(f"区域配置加载错误: {e}")
            return {}
    
    def extract_all_regional_pricing(self, html_content: str, config_path: str) -> CompleteRegionalAnalysis:
        """提取所有区域的定价数据 - 基于最终正确的逻辑"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        print("=== 开始Azure SSIS区域定价数据提取 ===")
        print("🔍 最终正确逻辑:")
        print("   1. 配置文件中有排除列表 → 排除指定表格")
        print("   2. 配置文件中为空数组 → 不排除任何表格")  
        print("   3. 配置文件中没有配置 → 不排除任何表格")
        print("")
        
        # 1. 发现HTML中所有的SSIS表格
        all_tables = self.discover_all_tables_in_html(soup)
        all_table_ids = list(all_tables.keys())
        
        # 2. 发现HTML中所有的区域
        all_regions = self.discover_regions_from_html(soup)
        
        # 3. 加载区域排除配置
        region_exclusions = self.load_region_configuration(config_path)
        
        print(f"\n配置文件中的SSIS排除规则: {len(region_exclusions)}个区域")
        for region_id, excludes in region_exclusions.items():
            print(f"  - {region_id}: {len(excludes)}个排除表格")
        print("")
        
        # 4. 为每个区域计算实际可用的表格
        regional_data = []
        full_service_count = 0
        partial_service_count = 0
        
        for region_id in all_regions:
            region_name = self.region_mapping.get(region_id, region_id)
            
            # 确定配置状态和排除列表
            if region_id in region_exclusions:
                excluded_table_ids = region_exclusions[region_id]
                if len(excluded_table_ids) == 0:
                    config_status = "✅ 配置为空数组"
                else:
                    config_status = f"⚠️ 配置排除 {len(excluded_table_ids)} 个表格"
            else:
                excluded_table_ids = []
                config_status = "✅ 无配置（默认全部显示）"
            
            # 计算该区域可用的表格：所有表格 - 排除的表格
            available_table_ids = [tid for tid in all_table_ids if tid not in excluded_table_ids]
            available_tables = [all_tables[tid] for tid in available_table_ids]
            
            total_configurations = sum(len(table.instances) for table in available_tables)
            
            # 确定服务级别
            if len(available_tables) >= 8:
                service_level = "🟢 完整服务"
                full_service_count += 1
            elif len(available_tables) >= 4:
                service_level = "🟡 标准服务"
                partial_service_count += 1
            elif len(available_tables) >= 2:
                service_level = "🟠 基础服务"
                partial_service_count += 1
            else:
                service_level = "🔴 无服务"
            
            regional_data.append(RegionalPricingData(
                region_id=region_id,
                region_name=region_name,
                config_status=config_status,
                excluded_tables=excluded_table_ids,
                available_tables=available_tables,
                total_available_configurations=total_configurations,
                service_level=service_level
            ))
            
            print(f"📍 {region_name}:")
            print(f"   {config_status}")
            print(f"   {service_level}")
            print(f"   排除表格: {len(excluded_table_ids)}个")
            print(f"   可用表格: {len(available_tables)}个") 
            print(f"   实例配置: {total_configurations}个")
            print("")
        
        # 5. 生成业务洞察
        business_insights = self._generate_business_insights(regional_data)
        
        return CompleteRegionalAnalysis(
            product_name="数据工厂 - SQL Server Integration Services",
            extraction_timestamp=datetime.now().isoformat(),
            total_regions=len(regional_data),
            full_service_regions=full_service_count,
            partial_service_regions=partial_service_count,
            regional_data=regional_data,
            business_insights=business_insights
        )
    
    def _generate_business_insights(self, regional_data: List[RegionalPricingData]) -> Dict[str, Any]:
        """生成业务洞察"""
        full_service_regions = [r.region_name for r in regional_data if "完整服务" in r.service_level]
        partial_service_regions = [r.region_name for r in regional_data if "基础服务" in r.service_level or "标准服务" in r.service_level]
        
        # 配置策略分析
        configured_regions = [r.region_name for r in regional_data if "配置排除" in r.config_status]
        unconfigured_regions = [r.region_name for r in regional_data if "无配置" in r.config_status or "空数组" in r.config_status]
        
        recommendations = []
        
        if full_service_regions:
            recommendations.append(f"推荐完整服务区域: {', '.join(full_service_regions)}")
            recommendations.append("这些区域提供全面的SSIS功能，适合所有类型的工作负载")
        
        if partial_service_regions:
            recommendations.append(f"受限服务区域: {', '.join(partial_service_regions)}")
            recommendations.append("这些区域仅提供高端配置，适合特定高性能需求")
        
        return {
            "full_service_regions": full_service_regions,
            "partial_service_regions": partial_service_regions,
            "configured_regions": configured_regions,
            "unconfigured_regions": unconfigured_regions,
            "recommendations": recommendations,
            "strategy_analysis": "只有老区域配置了排除策略，新区域默认提供完整服务"
        }

class RegionalDataExporter:
    """区域数据导出器"""
    
    @staticmethod
    def export_to_excel(analysis: CompleteRegionalAnalysis, file_path: str):
        """导出为Excel格式"""
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            
            # 区域概览表
            overview_data = []
            for region in analysis.regional_data:
                overview_data.append({
                    'region_id': region.region_id,
                    'region_name': region.region_name,
                    'config_status': region.config_status,
                    'service_level': region.service_level,
                    'excluded_table_count': len(region.excluded_tables),
                    'available_table_count': len(region.available_tables),
                    'total_configurations': region.total_available_configurations
                })
            
            overview_df = pd.DataFrame(overview_data)
            overview_df.to_excel(writer, sheet_name='区域概览', index=False)
            
            # 为每个区域创建详细的价格表
            for region in analysis.regional_data:
                if not region.available_tables:
                    continue
                    
                sheet_name = region.region_name[:30]  # Excel工作表名称限制
                pricing_data = []
                
                for table in region.available_tables:
                    for instance in table.instances:
                        row = {
                            '区域': region.region_name,
                            '配置状态': region.config_status,
                            '表格标题': table.title,
                            'VM系列': table.vm_series,
                            '许可类型': table.license_type,
                            '实例名称': instance.name,
                            'vCore数': instance.vcores,
                            '内存GB': instance.memory_gb,
                            '临时存储': instance.temp_storage,
                            '小时价格': instance.license_included_price.hourly_price,
                            '月度价格': instance.license_included_price.monthly_price,
                            '混合优惠小时价格': instance.hybrid_benefit.price.hourly_price if instance.hybrid_benefit else None,
                            '混合优惠月度价格': instance.hybrid_benefit.price.monthly_price if instance.hybrid_benefit else None,
                            '节省百分比': instance.hybrid_benefit.savings_percentage if instance.hybrid_benefit else None
                        }
                        pricing_data.append(row)
                
                if pricing_data:
                    pricing_df = pd.DataFrame(pricing_data)
                    pricing_df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            # 业务洞察汇总
            insights_data = [
                ['完整服务区域数', analysis.full_service_regions],
                ['受限服务区域数', analysis.partial_service_regions],
                ['总区域数', analysis.total_regions],
                ['策略分析', analysis.business_insights['strategy_analysis']]
            ]
            
            for i, recommendation in enumerate(analysis.business_insights['recommendations']):
                insights_data.append([f'建议{i+1}', recommendation])
            
            insights_df = pd.DataFrame(insights_data, columns=['指标', '值'])
            insights_df.to_excel(writer, sheet_name='业务洞察', index=False)
    
    @staticmethod
    def generate_summary_report(analysis: CompleteRegionalAnalysis) -> str:
        """生成摘要报告"""
        report = f"""
# Azure SSIS 区域定价分析报告

## 基本信息
- 产品: {analysis.product_name}
- 分析时间: {analysis.extraction_timestamp}
- 总区域数: {analysis.total_regions}
- 完整服务区域: {analysis.full_service_regions}个
- 受限服务区域: {analysis.partial_service_regions}个

## 区域服务级别详情
"""
        
        for region in analysis.regional_data:
            report += f"""
### {region.region_name}
- **服务级别**: {region.service_level}
- **配置状态**: {region.config_status}
- **排除表格**: {len(region.excluded_tables)}个
- **可用表格**: {len(region.available_tables)}个  
- **实例配置**: {region.total_available_configurations}个
"""
            
            if region.available_tables:
                vm_series = list(set(t.vm_series for t in region.available_tables))
                license_types = list(set(t.license_type for t in region.available_tables))
                report += f"- **支持系列**: {', '.join(vm_series)}\n"
                report += f"- **许可类型**: {', '.join(license_types)}\n"
        
        report += f"""
## 业务建议
"""
        for recommendation in analysis.business_insights['recommendations']:
            report += f"- {recommendation}\n"
        
        report += f"""
## 配置策略分析
{analysis.business_insights['strategy_analysis']}

### 完整服务区域
{', '.join(analysis.business_insights['full_service_regions'])}

### 受限服务区域  
{', '.join(analysis.business_insights['partial_service_regions'])}
"""
        
        return report

# 使用示例
def main():
    """主程序示例"""
    extractor = AzureRegionalPricingExtractor()
    exporter = RegionalDataExporter()
    
    # 读取HTML文件
    with open('ssis-index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 提取所有区域的定价数据
    print("=== 开始提取区域定价数据 ===")
    analysis = extractor.extract_all_regional_pricing(html_content, 'soft-category.json')
    
    # 导出数据
    exporter.export_to_excel(analysis, 'azure_ssis_regional_pricing_final.xlsx')
    
    # 生成摘要报告
    summary = exporter.generate_summary_report(analysis)
    with open('azure_ssis_regional_summary_final.md', 'w', encoding='utf-8') as f:
        f.write(summary)
    
    # 打印最终结果
    print(f"\n=== 提取完成 ===")
    print(f"总区域数: {analysis.total_regions}")
    print(f"完整服务区域: {analysis.full_service_regions}个")
    print(f"受限服务区域: {analysis.partial_service_regions}个")
    
    print(f"\n=== 区域服务级别 ===")
    for region in analysis.regional_data:
        print(f"{region.region_name}: {region.service_level}")
        print(f"  └─ {region.config_status}")

if __name__ == "__main__":
    main()