import re
from typing import Optional, Dict, Any
from dataclasses import dataclass
from universal_azure_price_extractor import BaseProductExtractor, ProductInstance, PriceDetail

@dataclass
class MySQLInstance(ProductInstance):
    """MySQL实例"""
    vcores: Optional[int] = None
    memory_gb: Optional[float] = None
    storage_type: Optional[str] = None
    iops: Optional[int] = None
    
    def __post_init__(self):
        # 确保specifications字典包含所有字段
        if not hasattr(self, 'specifications') or self.specifications is None:
            self.specifications = {}
        
        self.specifications.update({
            'vcores': self.vcores,
            'memory_gb': self.memory_gb,
            'storage_type': self.storage_type,
            'iops': self.iops
        })

class MySQLExtractor(BaseProductExtractor):
    """Azure Database for MySQL 价格提取器"""
    
    def get_product_name(self) -> str:
        return "Azure Database for MySQL"
    
    def get_table_id_pattern(self) -> str:
        return r"Azure_Database_For_MySQL"
    
    def parse_instance_from_row(self, row) -> Optional[ProductInstance]:
        """解析MySQL实例行"""
        try:
            cells = row.find_all(['td', 'th'])
            if len(cells) < 2:
                return None
            
            # 跳过表头行
            if any(cell.find('strong') for cell in cells):
                return None
            
            name = cells[0].get_text(strip=True)
            if not name or name in ['实例', '']:
                return None
            
            # 根据表格类型解析不同的列结构
            if len(cells) >= 4:  # 计算实例表格
                return self._parse_compute_instance(cells)
            elif len(cells) >= 2:  # 存储或其他类型表格
                return self._parse_storage_instance(cells)
            
            return None
            
        except Exception as e:
            print(f"MySQL实例解析错误: {e}")
            return None
    
    def _parse_compute_instance(self, cells) -> Optional[MySQLInstance]:
        """解析计算实例"""
        try:
            name = cells[0].get_text(strip=True)
            
            # 解析vCore
            vcores = None
            if len(cells) > 1:
                vcore_text = cells[1].get_text(strip=True)
                vcore_match = re.search(r'(\d+)', vcore_text)
                if vcore_match:
                    vcores = int(vcore_match.group(1))
            
            # 解析内存
            memory_gb = None
            if len(cells) > 2:
                memory_text = cells[2].get_text(strip=True)
                memory_match = re.search(r'([\d\.]+)\s*GB', memory_text)
                if memory_match:
                    memory_gb = float(memory_match.group(1))
            
            # 解析价格（通常在最后一列）
            price_text = cells[-1].get_text(strip=True)
            price = self.extract_price_from_text(price_text)
            
            if not price:
                return None
            
            return MySQLInstance(
                name=name,
                price=price,
                vcores=vcores,
                memory_gb=memory_gb,
                specifications={}
            )
            
        except Exception as e:
            print(f"计算实例解析错误: {e}")
            return None
    
    def _parse_storage_instance(self, cells) -> Optional[MySQLInstance]:
        """解析存储实例"""
        try:
            name = cells[0].get_text(strip=True)
            price_text = cells[1].get_text(strip=True)
            
            price = self.extract_price_from_text(price_text)
            if not price:
                return None
            
            # 判断存储类型
            storage_type = "存储"
            if "备份" in name:
                storage_type = "备份存储"
            elif "IOPS" in name:
                storage_type = "IOPS"
            
            return MySQLInstance(
                name=name,
                price=price,
                storage_type=storage_type,
                specifications={}
            )
            
        except Exception as e:
            print(f"存储实例解析错误: {e}")
            return None
    
    def extract_table_metadata(self, table, table_id: str) -> Dict[str, str]:
        """提取MySQL表格元数据"""
        try:
            # 查找表格标题
            title_element = table.find_previous(['h2', 'h3', 'h4'])
            title = title_element.get_text(strip=True) if title_element else ""
            
            # 根据标题和ID确定类别和服务层级
            category = self._determine_category(title, table_id)
            service_tier = self._determine_service_tier(title, table_id)
            
            return {
                'title': title,
                'category': category,
                'service_tier': service_tier
            }
            
        except Exception as e:
            print(f"元数据提取错误: {e}")
            return {'title': '', 'category': '', 'service_tier': ''}
    
    def _determine_category(self, title: str, table_id: str) -> str:
        """确定产品类别"""
        title_lower = title.lower()
        
        if "可突发" in title or "可突增" in title:
            return "可突发计算"
        elif "常规用途" in title or "d 系列" in title_lower:
            return "常规用途"
        elif "业务关键" in title or "e 系列" in title_lower:
            return "业务关键"
        elif "存储" in title:
            return "存储"
        elif "备份" in title:
            return "备份存储"
        elif "iops" in title_lower:
            return "IOPS"
        else:
            return "其他"
    
    def _determine_service_tier(self, title: str, table_id: str) -> str:
        """确定服务层级"""
        if "灵活服务器" in title:
            return "灵活服务器"
        elif "单一服务器" in title:
            return "单一服务器"
        else:
            return "标准"
    
    def extract_price_from_text(self, price_text: str) -> Optional[PriceDetail]:
        """MySQL特定的价格解析"""
        try:
            # 清理文本
            price_text = price_text.replace('\n', ' ').replace('\r', ' ')
            
            # 匹配各种价格格式
            patterns = [
                r'￥\s*([\d,\.]+)/小时',  # ￥ 0.0723/小时
                r'￥\s*([\d,\.]+)\s*/\s*小时',  # ￥ 0.0723 / 小时
                r'￥\s*([\d,\.]+)',  # ￥ 0.0723
            ]
            
            for pattern in patterns:
                match = re.search(pattern, price_text)
                if match:
                    price = float(match.group(1).replace(',', ''))
                    
                    # 计算月度价格
                    monthly_price = price * 24 * 31
                    
                    return PriceDetail(
                        hourly_price=price,
                        monthly_price=monthly_price,
                        currency="CNY",
                        pricing_model="现用现付"
                    )
            
            return None
            
        except Exception as e:
            print(f"MySQL价格解析错误: {price_text}, 错误: {e}")
            return None

# 使用示例
def main():
    """MySQL提取器使用示例"""
    extractor = MySQLExtractor()
    
    # 读取HTML文件
    with open('mysql-index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 提取所有区域的定价数据
    print("=== 开始提取MySQL区域定价数据 ===")
    analysis = extractor.extract_all_regional_pricing(html_content, 'soft-category.json')
    
    # 输出结果
    print(f"\n=== 提取完成 ===")
    print(f"总区域数: {analysis.total_regions}")
    print(f"完整服务区域: {analysis.full_service_regions}个")
    print(f"受限服务区域: {analysis.partial_service_regions}个")
    
    print(f"\n=== 区域服务级别 ===")
    for region in analysis.regional_data:
        print(f"{region.region_name}: {region.service_level}")
        print(f"  └─ {region.config_status}")
        print(f"  └─ 可用表格: {len(region.available_tables)}个")
        print(f"  └─ 实例配置: {region.total_available_configurations}个")

if __name__ == "__main__":
    main()
