#!/usr/bin/env python3
"""
统一的HTML提取框架 - 整合配置驱动和AI增强的解决方案
"""

import os
import json
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from datetime import datetime

from flexible_schema_extractor import <PERSON>hemaManager, FlexibleExtractor
from ai_enhanced_extractor import AdaptiveExtractor, AITableAnalyzer

@dataclass
class ExtractionResult:
    """提取结果"""
    product_name: str
    extraction_method: str  # 'schema-based', 'adaptive', 'hybrid'
    success: bool
    tables: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    errors: List[str] = None

class UnifiedExtractionFramework:
    """统一提取框架"""
    
    def __init__(self, schema_dir: str = "schemas"):
        self.schema_manager = SchemaManager(schema_dir)
        self.flexible_extractor = FlexibleExtractor(self.schema_manager)
        self.adaptive_extractor = AdaptiveExtractor()
        self.ai_analyzer = AITableAnalyzer()
        
        # 提取策略配置
        self.extraction_strategies = {
            'schema_first': self._schema_first_strategy,
            'adaptive_first': self._adaptive_first_strategy,
            'hybrid': self._hybrid_strategy
        }
    
    def extract_product_data(self, 
                           html_content: str, 
                           product_name: str,
                           strategy: str = 'hybrid') -> ExtractionResult:
        """统一的产品数据提取入口"""
        
        print(f"🔍 开始提取 {product_name} 数据 (策略: {strategy})")
        
        if strategy not in self.extraction_strategies:
            return ExtractionResult(
                product_name=product_name,
                extraction_method='error',
                success=False,
                tables=[],
                metadata={},
                errors=[f"未知的提取策略: {strategy}"]
            )
        
        try:
            return self.extraction_strategies[strategy](html_content, product_name)
        except Exception as e:
            return ExtractionResult(
                product_name=product_name,
                extraction_method='error',
                success=False,
                tables=[],
                metadata={},
                errors=[str(e)]
            )
    
    def _schema_first_strategy(self, html_content: str, product_name: str) -> ExtractionResult:
        """Schema优先策略"""
        errors = []
        
        # 尝试使用已有schema
        if product_name in self.schema_manager.list_products():
            try:
                result = self.flexible_extractor.extract_product_data(html_content, product_name)
                
                # 转换FlexibleTable对象为字典
                tables_data = []
                for table in result['tables']:
                    if hasattr(table, '__dict__'):
                        tables_data.append(asdict(table))
                    else:
                        tables_data.append(table)

                return ExtractionResult(
                    product_name=product_name,
                    extraction_method='schema-based',
                    success=True,
                    tables=tables_data,
                    metadata=result.get('extraction_metadata', {})
                )
            except Exception as e:
                errors.append(f"Schema提取失败: {e}")
        
        # 如果schema提取失败，回退到自适应提取
        try:
            result = self.adaptive_extractor.extract_unknown_product(html_content, product_name)
            
            return ExtractionResult(
                product_name=product_name,
                extraction_method='adaptive-fallback',
                success=True,
                tables=result['tables'],
                metadata=result,
                errors=errors
            )
        except Exception as e:
            errors.append(f"自适应提取失败: {e}")
            
            return ExtractionResult(
                product_name=product_name,
                extraction_method='failed',
                success=False,
                tables=[],
                metadata={},
                errors=errors
            )
    
    def _adaptive_first_strategy(self, html_content: str, product_name: str) -> ExtractionResult:
        """自适应优先策略"""
        try:
            result = self.adaptive_extractor.extract_unknown_product(html_content, product_name)
            
            return ExtractionResult(
                product_name=product_name,
                extraction_method='adaptive',
                success=True,
                tables=result['tables'],
                metadata=result
            )
        except Exception as e:
            return ExtractionResult(
                product_name=product_name,
                extraction_method='adaptive-failed',
                success=False,
                tables=[],
                metadata={},
                errors=[str(e)]
            )
    
    def _hybrid_strategy(self, html_content: str, product_name: str) -> ExtractionResult:
        """混合策略"""
        errors = []
        
        # 1. 首先尝试schema-based提取
        schema_result = None
        if product_name in self.schema_manager.list_products():
            try:
                schema_data = self.flexible_extractor.extract_product_data(html_content, product_name)

                # 转换FlexibleTable对象为字典
                tables_data = []
                for table in schema_data['tables']:
                    if hasattr(table, '__dict__'):
                        tables_data.append(asdict(table))
                    else:
                        tables_data.append(table)

                schema_result = ExtractionResult(
                    product_name=product_name,
                    extraction_method='schema-based',
                    success=True,
                    tables=tables_data,
                    metadata=schema_data.get('extraction_metadata', {})
                )
            except Exception as e:
                errors.append(f"Schema提取失败: {e}")
        
        # 2. 同时进行自适应提取
        adaptive_result = None
        try:
            adaptive_data = self.adaptive_extractor.extract_unknown_product(html_content, product_name)
            adaptive_result = ExtractionResult(
                product_name=product_name,
                extraction_method='adaptive',
                success=True,
                tables=adaptive_data['tables'],
                metadata=adaptive_data
            )
        except Exception as e:
            errors.append(f"自适应提取失败: {e}")
        
        # 3. 选择最佳结果或合并结果
        return self._select_best_result(schema_result, adaptive_result, errors)
    
    def _select_best_result(self, 
                          schema_result: Optional[ExtractionResult], 
                          adaptive_result: Optional[ExtractionResult],
                          errors: List[str]) -> ExtractionResult:
        """选择最佳提取结果"""
        
        # 如果只有一个成功，返回成功的那个
        if schema_result and schema_result.success and not (adaptive_result and adaptive_result.success):
            return schema_result
        
        if adaptive_result and adaptive_result.success and not (schema_result and schema_result.success):
            return adaptive_result
        
        # 如果都成功，比较质量
        if schema_result and schema_result.success and adaptive_result and adaptive_result.success:
            schema_score = self._calculate_result_quality(schema_result)
            adaptive_score = self._calculate_result_quality(adaptive_result)
            
            if schema_score >= adaptive_score:
                schema_result.metadata['alternative_method'] = 'adaptive'
                schema_result.metadata['alternative_score'] = adaptive_score
                return schema_result
            else:
                adaptive_result.metadata['alternative_method'] = 'schema-based'
                adaptive_result.metadata['alternative_score'] = schema_score
                return adaptive_result
        
        # 如果都失败，返回失败结果
        return ExtractionResult(
            product_name=schema_result.product_name if schema_result else adaptive_result.product_name if adaptive_result else "unknown",
            extraction_method='hybrid-failed',
            success=False,
            tables=[],
            metadata={},
            errors=errors
        )
    
    def _calculate_result_quality(self, result: ExtractionResult) -> float:
        """计算结果质量分数"""
        score = 0.0
        
        # 表格数量
        table_count = len(result.tables)
        score += min(table_count * 0.1, 1.0)  # 最多1分
        
        # 实例数量
        total_instances = sum(len(table.get('instances', [])) for table in result.tables)
        score += min(total_instances * 0.01, 1.0)  # 最多1分
        
        # 置信度（如果有）
        if 'confidence' in result.metadata:
            score += result.metadata['confidence']
        
        # 数据完整性
        complete_tables = sum(1 for table in result.tables if table.get('instances'))
        if table_count > 0:
            completeness = complete_tables / table_count
            score += completeness
        
        return score
    
    def generate_schema_from_adaptive_result(self, 
                                           adaptive_result: ExtractionResult,
                                           output_path: str = None) -> Dict[str, Any]:
        """从自适应提取结果生成schema"""
        
        if not adaptive_result.success:
            raise ValueError("无法从失败的提取结果生成schema")
        
        # 分析提取结果，生成schema
        schema_data = {
            'product_name': adaptive_result.product_name,
            'generated_from': 'adaptive_extraction',
            'generation_time': datetime.now().isoformat(),
            'base_fields': [
                {'name': 'name', 'type': 'text', 'required': True},
                {'name': 'price', 'type': 'price', 'required': True}
            ],
            'table_schemas': []
        }
        
        # 分析每个表格
        for table in adaptive_result.tables:
            if 'extraction_metadata' in table:
                metadata = table['extraction_metadata']
                
                table_schema = {
                    'table_id_pattern': table['table_id'],
                    'title_selector': 'h2',  # 默认值
                    'category_rules': {table['category']: table['category']},
                    'service_tier_rules': {'标准': 'standard'},
                    'column_mapping': {},
                    'price_patterns': [
                        r'￥\s*([\d,\.]+)/小时',
                        r'￥\s*([\d,\.]+)'
                    ]
                }
                
                # 从column_types生成column_mapping
                if 'column_types' in metadata:
                    for col_idx, col_type in metadata['column_types'].items():
                        table_schema['column_mapping'][col_idx] = {
                            'name': f'column_{col_idx}',
                            'type': col_type,
                            'required': col_type in ['name', 'price']
                        }
                
                schema_data['table_schemas'].append(table_schema)
        
        # 保存schema文件
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(schema_data, f, ensure_ascii=False, indent=2)
            print(f"✓ 生成schema文件: {output_path}")
        
        return schema_data
    
    def validate_extraction_result(self, result: ExtractionResult) -> Dict[str, Any]:
        """验证提取结果"""
        validation = {
            'is_valid': True,
            'warnings': [],
            'errors': [],
            'statistics': {}
        }
        
        # 基本验证
        if not result.tables:
            validation['errors'].append("未提取到任何表格")
            validation['is_valid'] = False
        
        # 统计信息
        total_instances = sum(len(table.get('instances', [])) for table in result.tables)
        validation['statistics'] = {
            'total_tables': len(result.tables),
            'total_instances': total_instances,
            'average_instances_per_table': total_instances / len(result.tables) if result.tables else 0
        }
        
        # 数据质量检查
        for i, table in enumerate(result.tables):
            instances = table.get('instances', [])
            
            if not instances:
                validation['warnings'].append(f"表格 {i+1} 没有实例数据")
            
            # 检查价格数据
            instances_with_price = sum(1 for inst in instances if inst.get('price'))
            if instances_with_price < len(instances) * 0.8:  # 80%的实例应该有价格
                validation['warnings'].append(f"表格 {i+1} 中 {len(instances) - instances_with_price} 个实例缺少价格数据")
        
        return validation
    
    def export_extraction_report(self, 
                               results: List[ExtractionResult], 
                               output_path: str):
        """导出提取报告"""
        
        report = {
            'extraction_time': datetime.now().isoformat(),
            'total_products': len(results),
            'successful_extractions': sum(1 for r in results if r.success),
            'failed_extractions': sum(1 for r in results if not r.success),
            'extraction_methods': {},
            'products': []
        }
        
        # 统计提取方法
        for result in results:
            method = result.extraction_method
            if method not in report['extraction_methods']:
                report['extraction_methods'][method] = 0
            report['extraction_methods'][method] += 1
        
        # 产品详情
        for result in results:
            validation = self.validate_extraction_result(result)
            
            product_info = {
                'product_name': result.product_name,
                'extraction_method': result.extraction_method,
                'success': result.success,
                'tables_count': len(result.tables),
                'total_instances': sum(len(table.get('instances', [])) for table in result.tables),
                'validation': validation,
                'errors': result.errors or []
            }
            
            report['products'].append(product_info)
        
        # 保存报告
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 导出提取报告: {output_path}")

# 使用示例
def main():
    """统一框架使用示例"""
    framework = UnifiedExtractionFramework()
    
    # 测试不同的提取策略
    test_files = [
        ('ssis-index.html', 'Data Factory SSIS'),
        ('mysql-index.html', 'Azure Database for MySQL')
    ]
    
    results = []
    
    for html_file, product_name in test_files:
        if os.path.exists(html_file):
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 测试混合策略
            result = framework.extract_product_data(html_content, product_name, 'hybrid')
            results.append(result)
            
            print(f"\n{product_name}:")
            print(f"  提取方法: {result.extraction_method}")
            print(f"  成功: {result.success}")
            print(f"  表格数: {len(result.tables)}")
            
            if result.errors:
                print(f"  错误: {result.errors}")
    
    # 导出报告
    framework.export_extraction_report(results, 'extraction_report.json')

if __name__ == "__main__":
    main()
