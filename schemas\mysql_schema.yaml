base_fields:
- name: name
  required: true
  type: text
- name: vcores
  pattern: (\d+)
  required: false
  type: number
- name: memory_gb
  pattern: ([\d\.]+)\s*GB
  required: false
  type: number
deduplication_rules:
  enabled: true
  signature_fields:
  - category
  - service_tier
  - instances_count
  - instance_signatures
product_name: Azure Database for MySQL
table_schemas:
- category_rules:
    D.*系列: 常规用途
    E.*系列: 业务关键
    IOPS: IOPS
    可突发: 可突发计算
    备份: 备份存储
    存储: 存储
  column_mapping:
    0:
      name: instance_name
      required: true
      type: text
    1:
      name: vcores_or_unit
      type: text
    2:
      name: memory_or_description
      type: text
    3:
      name: price
      required: true
      type: price
  price_patterns:
  - ￥\s*([\d,\.]+)/小时
  - 每百万\s*IOPS\s*￥([\d,\.]+)
  - ￥\s*([\d,\.]+)
  service_tier_rules:
    单一服务器: 单一服务器
    灵活服务器: 灵活服务器
  table_id_pattern: Azure_Database_For_MySQL
  title_selector: h2
