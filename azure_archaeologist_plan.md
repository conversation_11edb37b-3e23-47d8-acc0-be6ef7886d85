# AzureCNArchaeologist 项目实施方案文档

## 项目概述

### 项目背景
Azure中国定价网站 (https://www.azure.cn/pricing/) 原维护团队已解散，前端JavaScript代码丢失。项目团队已获得完整的HTML源码文件，需要通过"HTML解析式考古"，从大量HTML文件中提取结构化数据，重建整个产品价格和计算器页面系统。

### 项目目标
1. **智能解析**：从现有HTML文件中智能提取所有产品信息、价格数据、描述和图片路径
2. **深度建模**：构建结构化的数据模型，支持复杂定价计算逻辑和明细展示
3. **CMS就绪**：输出标准化数据格式，便于后续CMS系统导入和团队手动维护

### 项目范围
- **HTML解析模块**：批量HTML文件智能解析
- **数据提取模块**：结构化数据提取和清洗
- **定价重建模块**：复杂计算逻辑重构和明细化
- **导出模块**：多格式数据导出和CMS适配

## 技术架构设计

### 核心技术栈
- **编程语言**：Python 3.11+
- **Python 包管理**：poetry / uv
- **HTML解析**：BeautifulSoup + lxml + selectolax
- **数据处理**：pandas + numpy + jieba
- **传统RAG组件**：
  - **Embedding模型**：sentence-transformers / text-embedding-3-large / qwen3-embedding
  - **向量存储**：faiss / chromadb / qdrant / milvus / azure-vector-store?
  - **Rerank模型**：cross-encoder / cohere-rerank / qwen3-rerank
- **大上下文RAG**：transformers + deepseek-api + openai-api
- **数据存储**：Postgresql + MongoDB + SQLAlchemy + JSON
- **文档处理**：python-docx + PyPDF2 + markdown + Mineru
- **可视化**：matplotlib + plotly + graphviz
- **性能优化**：multiprocessing + asyncio + redis + caching

### 系统架构
```
AzureCNArchaeologist/
├── analysis/           # HTML分析模块
├── parsing/            # HTML解析模块
├── extraction/         # 数据提取模块
├── processing/         # 数据处理模块
├── modeling/           # 定价建模模块
├── rag_preparation/    # RAG数据准备模块
├── validation/         # 数据验证模块
├── export/             # 数据导出模块
├── utils/              # 工具类库
├── config/             # 配置文件
├── data/               # 数据目录
│   ├── html_source/    # 原始HTML文件
│   ├── parsed/         # 解析后数据
│   ├── processed/      # 处理后数据
│   ├── rag_ready/      # RAG就绪数据
│   ├── vectors/        # 向量化数据
│   ├── metadata/       # 元数据文件
│   ├── images/         # 图片资源路径
│   └── exports/        # 最终输出
└── docs/               # 文档目录
```

## 阶段一：HTML文件分析与分类 - 优化实施方案

### ⏰ 优化时间安排（3天完成）

**总体优化理念：**
- 提升实际可操作性，避免原方案过于理论化
- 增强风险预控能力，提前识别技术难点
- 建立科学的数据质量评估体系
- 为第二阶段制定精准的解析策略

```mermaid
gantt
    title 阶段一优化时间线
    dateFormat  YYYY-MM-DD
    section Day 1
    文件系统深度分析    :done, task1, 2024-01-01, 4h
    编码检测与修复      :done, task2, 2024-01-01, 4h
    section Day 2
    内容结构模式识别    :active, task3, 2024-01-02, 8h
    section Day 3
    智能分类与优先级    :task4, 2024-01-03, 4h
    解析策略制定        :task5, 2024-01-03, 4h
```

### 🔍 Day 1: 文件系统深度分析与环境准备

#### 上午任务 (4小时): 实际文件路径验证和统计

**目标**: 建立准确的文件基础信息，避免后续处理中的路径和编码问题

**技术架构**:
```python
"""
HTML文件系统深度分析模块 - 主体框架
"""

@dataclass
class FileInfo:
    """文件信息标准化结构"""
    path: str
    relative_path: str
    name: str
    size: int
    modified_time: datetime
    directory: str
    encoding: Optional[str] = None
    is_valid_html: Optional[bool] = None

class HTMLFileSystemAnalyzer:
    """HTML文件系统深度分析器 - 第一阶段核心组件"""
    
    def __init__(self, base_path: str = "./current_prod_html/zh-cn/pricing/details/"):
        self.base_path = Path(base_path)
        self.analysis_results = {}
        
    def perform_comprehensive_analysis(self) -> Dict:
        """执行全面的文件系统分析"""
        # 主要流程：
        # 1. 路径存在性验证
        # 2. 递归扫描文件结构  
        # 3. 文件统计分析
        # 4. 编码批量检测
        # 5. 文件完整性检查
        # 6. 生成分析报告
        pass
    
    def _scan_directory_structure(self) -> Dict:
        """扫描目录结构，识别Azure服务目录"""
        pass
    
    def _extract_service_id(self, relative_path: Path) -> Optional[str]:
        """从路径提取Azure服务ID"""
        pass
    
    def _calculate_file_statistics(self, file_structure: Dict) -> Dict:
        """计算详细的文件统计信息"""
        pass
    
    def _detect_file_encodings(self, html_files: List[Dict]) -> Dict:
        """批量检测文件编码"""
        pass
    
    def _check_file_integrity(self, html_files: List[Dict]) -> Dict:
        """检查文件完整性"""
        pass
    
    def _generate_recommendations(self, file_structure: Dict, file_stats: Dict) -> List[str]:
        """生成处理建议"""
        pass
    
    def _save_analysis_report(self, report: Dict):
        """保存分析报告到JSON和CSV格式"""
        pass
```

#### 下午任务 (4小时): 编码检测与修复机制

**目标**: 建立容错性强的文件读取机制，解决中文编码问题

**技术架构**:
```python
class RobustFileReader:
    """容错性文件读取器"""
    
    def __init__(self):
        self.encoding_cache = {}
        self.failed_files = []
        
    def read_html_file(self, file_path: str) -> Tuple[str, str]:
        """容错读取HTML文件"""
        # 主要策略：
        # 1. 检查编码缓存
        # 2. 尝试常见编码(utf-8, gbk, gb2312, utf-16, big5)
        # 3. 使用chardet自动检测
        # 4. 强制读取(errors='ignore')
        pass
    
    def _try_encoding(self, file_path: str, encoding: str) -> Optional[str]:
        """尝试指定编码读取文件"""
        pass
    
    def _detect_encoding_with_chardet(self, file_path: str) -> Optional[Tuple[str, str]]:
        """使用chardet检测文件编码"""
        pass
```

### 🔍 Day 2: 内容结构模式识别与分析

#### 全天任务 (8小时): HTML结构深度分析

**目标**: 识别Azure定价页面的结构模式，为解析策略制定提供基础

**技术架构**:
```python
from bs4 import BeautifulSoup
from collections import defaultdict, Counter

class HTMLStructureAnalyzer:
    """HTML结构模式分析器"""
    
    def __init__(self):
        self.structure_patterns = defaultdict(list)
        self.table_patterns = []
        self.pricing_indicators = []
        
    def analyze_html_structures(self, html_files: List[str]) -> Dict:
        """分析HTML结构模式"""
        # 主要流程：
        # 1. 页面结构分析
        # 2. 表格结构分析  
        # 3. 定价信息模式分析
        # 4. CSS类名分析
        # 5. JavaScript依赖分析
        # 6. 生成解析规则
        pass
    
    def _select_representative_files(self, html_files: List[str]) -> List[str]:
        """选择代表性文件进行分析"""
        pass
    
    def _analyze_page_structure(self, soup: BeautifulSoup) -> Dict:
        """分析页面整体结构"""
        pass
    
    def _analyze_table_structures(self, soup: BeautifulSoup) -> Dict:
        """分析表格结构 - 关键功能"""
        # 主要分析：
        # 1. 表格类型识别
        # 2. 表头结构分析
        # 3. 定价表格专项分析
        # 4. 合并单元格处理
        pass
    
    def _contains_pricing_data(self, table) -> bool:
        """检测表格是否包含定价信息"""
        pass
    
    def _analyze_table_headers(self, table) -> Dict:
        """分析表头结构，处理多层表头和合并单元格"""
        pass
    
    def _classify_header_type(self, header_text: str) -> str:
        """分类表头类型(pricing/cpu/memory/storage等)"""
        pass
    
    def _analyze_pricing_patterns(self, soup: BeautifulSoup) -> Dict:
        """分析定价信息模式"""
        pass
    
    def _analyze_css_classes(self, soup: BeautifulSoup) -> Dict:
        """分析CSS类名模式"""
        pass
    
    def _analyze_javascript_dependencies(self, soup: BeautifulSoup) -> List[str]:
        """分析JavaScript依赖"""
        pass
    
    def _generate_parsing_rules(self, analysis_results: Dict) -> Dict:
        """生成解析规则"""
        pass
```

### 🔍 Day 3: 智能分类与解析策略制定

#### 上午任务 (4小时): 智能文件分类

**技术架构**:
```python
class IntelligentFileClassifier:
    """智能文件分类器"""
    
    def __init__(self):
        self.classification_rules = {
            'pricing_detail_page': {
                'indicators': ['pricing-table', 'price-calculator', 'cost-estimation'],
                'required_elements': ['table'],
                'content_keywords': ['价格', '定价', '费用']
            },
            'service_overview_page': {
                'indicators': ['service-overview', 'product-intro'],
                'content_keywords': ['概述', '介绍', '功能', '特性']
            },
            'calculator_page': {
                'indicators': ['calculator', 'estimator'],
                'required_elements': ['form', 'input'],
                'content_keywords': ['计算器', '估算']
            }
        }
    
    def classify_files(self, html_files: List[Dict]) -> Dict:
        """对HTML文件进行智能分类"""
        # 主要流程：
        # 1. 文件分类识别
        # 2. 数据质量评估
        # 3. 处理难度评估
        # 4. 优先级计算
        # 5. 生成处理顺序
        pass
    
    def _classify_single_file(self, soup: BeautifulSoup, file_info: Dict) -> Dict:
        """分类单个文件"""
        pass
    
    def _assess_data_quality(self, soup: BeautifulSoup, file_info: Dict) -> float:
        """评估数据质量 (0-10分)"""
        # 评估维度：
        # - 基础HTML结构 (2分)
        # - 表格质量 (2分)
        # - 内容丰富度 (3分)
        # - 文件大小合理性 (1分)
        # - CSS和结构完整性 (2分)
        pass
    
    def _assess_parsing_difficulty(self, soup: BeautifulSoup) -> int:
        """评估解析难度 (1-5级)"""
        pass
    
    def _calculate_processing_priority(self, classification: Dict, 
                                     quality_score: float, file_info: Dict) -> int:
        """计算处理优先级 (1-10)"""
        # 优先级因素：
        # - Azure服务重要性 (tier1/tier2/tier3)
        # - 数据质量分数
        # - 文件类型重要性
        # - 处理复杂度
        pass
    
    def _extract_service_from_path(self, relative_path: str) -> str:
        """从文件路径提取服务标识"""
        pass
    
    def _generate_processing_order(self, classifications: Dict) -> List[Dict]:
        """生成最优处理顺序"""
        pass
```

#### 下午任务 (4小时): 解析策略制定

**输出物架构**:
```python
def generate_parsing_strategy_map() -> Dict:
    """生成解析策略映射"""
    return {
        "tier1_services": {
            "virtual-machines": {
                "parsing_strategy": "complex_table_parser",
                "expected_challenges": ["merged_cells", "dynamic_pricing", "multiple_regions"],
                "fallback_strategy": "simple_text_parser",
                "quality_threshold": 0.8
            },
            "storage": {
                "parsing_strategy": "tiered_pricing_parser", 
                "expected_challenges": ["usage_tiers", "redundancy_options"],
                "fallback_strategy": "pattern_matching_parser",
                "quality_threshold": 0.7
            }
        },
        "parsing_complexity_levels": {
            "level_1_simple": {
                "description": "简单静态表格",
                "applicable_files": "质量分>8，单一表格",
                "processing_time_estimate": "10-30秒"
            },
            "level_2_complex": {
                "description": "复杂表格结构", 
                "applicable_files": "质量分6-8，多表格或合并单元格",
                "processing_time_estimate": "30-60秒"
            },
            "level_3_dynamic": {
                "description": "动态内容页面",
                "applicable_files": "质量分<6，需要特殊处理", 
                "processing_time_estimate": "60-120秒"
            }
        },
        "error_handling_strategies": {
            "encoding_issues": "multi_encoding_detection",
            "parsing_failures": "graceful_degradation", 
            "missing_data": "partial_extraction_with_flags"
        }
    }

class StrategyConfigGenerator:
    """解析策略配置生成器"""
    
    def __init__(self):
        pass
    
    def generate_service_specific_configs(self, analysis_results: Dict) -> Dict:
        """基于分析结果生成服务专用配置"""
        pass
    
    def generate_error_recovery_chains(self) -> Dict:
        """生成错误恢复链配置"""
        pass
    
    def generate_quality_thresholds(self) -> Dict:
        """生成质量阈值配置"""
        pass
```

### 📊 优化后的输出物

#### 1. 增强版文件清单 (html_inventory_enhanced.csv)
```csv
file_path,service_id,service_name_cn,classification_type,has_pricing_table,content_quality_score,parsing_difficulty,processing_priority,estimated_processing_time_sec,file_size_mb,encoding,last_modified,commercial_tier
```

#### 2. 数据质量预评估报告 (data_quality_pre_assessment.json)
```json
{
  "assessment_summary": {
    "total_files_assessed": 500,
    "average_quality_score": 7.2,
    "high_quality_files": 300,
    "medium_quality_files": 150,
    "low_quality_files": 50
  },
  "quality_issues": {
    "encoding_problems": 12,
    "corrupted_files": 3,
    "empty_files": 5,
    "suspicious_content": 8
  },
  "recommendations": [
    "优先处理high_quality_files，确保基础数据可靠性",
    "为encoding_problems建立专门的转换机制",
    "low_quality_files需要人工审查和特殊处理策略"
  ]
}
```

#### 3. 解析策略配置 (parsing_strategy_config.json)
```json
{
  "strategy_selection_rules": {
    "quality_score >= 8": "standard_parser",
    "6 <= quality_score < 8": "robust_parser",
    "quality_score < 6": "fallback_parser"
  },
  "service_specific_configs": {
    "virtual-machines": {
      "expected_table_types": ["vm_sizes", "pricing_tiers", "regional_pricing"],
      "special_handling": ["merged_cells", "dynamic_content"],
      "timeout_seconds": 60
    }
  },
  "error_recovery": {
    "max_retries": 3,
    "fallback_chain": ["complex_parser", "simple_parser", "text_extraction"]
  }
}
```

### ⚠️ 风险控制优化

#### 1. 编码问题应对机制
- **多编码检测**: UTF-8, GBK, GB2312, Big5自动检测
- **智能修复**: 常见编码错误的自动修复
- **失败处理**: 记录失败文件，提供手动处理建议

#### 2. 数据质量控制
- **质量评分**: 0-10分量化评估系统
- **阈值控制**: 低于阈值的文件标记为需要特殊处理
- **人工审查**: 建立人工审查的优先级队列

#### 3. 性能优化策略
- **批处理**: 按服务分组并行处理
- **内存管理**: 大文件的流式处理
- **进度监控**: 实时处理进度和异常报告

### ✅ 成功标准

#### 量化指标
- **文件扫描完成率**: 100% (必须达到)
- **数据质量评估覆盖率**: 100%
- **编码问题解决率**: >95%
- **分类准确率**: >90% (通过抽样验证)
- **处理策略配置完成率**: 100%

#### 里程碑验收
- **Day 1 End**: 完成文件系统分析，获得准确清单和编码方案
- **Day 2 End**: 完成结构模式分析，建立解析规则库
- **Day 3 End**: 完成分类和策略制定，为第二阶段做好充分准备

## 阶段二：HTML解析与数据提取 - 优化实施方案

### ⏰ 优化时间安排（5天完成，原方案过于乐观）

**总体优化理念：**
- 分解复杂任务，降低技术风险
- 建立多层次容错机制，提高解析成功率
- 专门针对Azure定价表格的复杂性优化算法
- 强化中文文本处理和价格模式识别

```mermaid
gantt
    title 阶段二优化时间线
    dateFormat  YYYY-MM-DD
    section Day 1-2
    核心解析引擎开发     :done, task1, 2024-01-04, 2d
    section Day 3
    定价表格专项解析     :active, task2, 2024-01-06, 1d
    section Day 4
    服务信息提取器       :task3, 2024-01-07, 1d
    section Day 5
    集成测试与优化       :task4, 2024-01-08, 1d
```

### 📐 核心架构设计重构

#### 多策略解析器架构
```python
# 主体框架设计 - 不包含完整实现
class ParseStrategy(Enum):
    SIMPLE_TABLE = "simple_table"           # 简单静态表格
    COMPLEX_TABLE = "complex_table"         # 复杂表格(合并单元格)
    DYNAMIC_CONTENT = "dynamic_content"     # JavaScript动态内容
    CALCULATOR_PAGE = "calculator_page"     # 计算器页面
    OVERVIEW_PAGE = "overview_page"         # 概述页面

class ParseResult:
    """解析结果标准化数据结构"""
    success: bool
    data: Dict
    errors: List[str]
    warnings: List[str]
    strategy_used: ParseStrategy
    confidence_score: float  # 0-1，解析结果的置信度
    processing_time: float

class BaseParser(ABC):
    """解析器基类 - 策略模式"""
    @abstractmethod
    def can_parse(self, html_content: str, file_info: Dict) -> float:
        """判断是否能解析，返回置信度分数"""
        pass
    
    @abstractmethod
    def parse(self, html_content: str, file_info: Dict) -> ParseResult:
        """执行解析"""
        pass
```

### 🔍 Day 1-2: 核心解析引擎开发

#### 技术架构优化要点

**1. 智能策略选择机制**
- **基于第一阶段分析结果**：利用文件分类和质量评分自动选择解析策略
- **动态策略调整**：解析失败时自动降级到简单策略
- **置信度评估**：每个解析器返回0-1的置信度分数

**2. Azure定价表格专用解析器**
```python
class AzurePricingTableParser(BaseParser):
    """Azure定价表格专用解析器 - 核心难点"""
    
    def __init__(self):
        # Azure定价表格特征模式
        self.table_indicators = [
            'pricing-table', 'price-table', 'cost-table',
            '定价表', '价格表', '费用表'
        ]
        
        # 复杂表格特征识别
        self.complex_features = [
            'colspan', 'rowspan',  # 合并单元格
            'thead', 'tbody',      # 复杂表头
            'data-price',          # 动态价格属性
        ]
    
    def can_parse(self, html_content: str, file_info: Dict) -> float:
        """评估解析能力 - 基于表格复杂度"""
        # 实现逻辑：检查表格特征、内容丰富度、结构复杂度
        pass
    
    def parse(self, html_content: str, file_info: Dict) -> ParseResult:
        """执行定价表格解析"""
        # 主要流程：
        # 1. 提取服务基本信息
        # 2. 定价表格解析 - 核心功能
        # 3. 服务特性提取
        # 4. 技术规格提取
        pass
```

**3. 中文价格文本解析算法**
```python
class ChinesePriceParser:
    """中文价格文本解析器 - 关键技术点"""
    
    def __init__(self):
        # 中文价格模式匹配规则
        self.price_patterns = [
            r'¥?(\d+(?:\.\d+)?)\s*(?:元)?/?([小时月年天])',  # ¥1.23/小时
            r'(\d+(?:,\d{3})*(?:\.\d+)?)\s*(?:元|CNY)\s*/?([小时月年天])',  # 123,456.78 元/月
            r'每([小时月年天])\s*¥?(\d+(?:\.\d+)?)',  # 每小时¥1.23
            r'(免费|包含|included|free)',  # 免费服务
            r'(按需|on-demand)',  # 按需付费
        ]
        
        # 单位标准化映射
        self.unit_mapping = {
            '小时': 'hour', '月': 'month', '年': 'year', '天': 'day',
            'GB': 'gb', 'TB': 'tb', '次': 'request'
        }
    
    def parse_price_text(self, price_text: str) -> Dict:
        """解析价格文本 - 核心算法"""
        # 返回标准化的价格信息结构
        pass
```

**4. 批量处理与性能优化**
```python
class BatchProcessor:
    """批量处理器 - 性能优化核心"""
    
    def __init__(self, max_workers=4, max_memory_mb=2048):
        self.max_workers = max_workers
        self.max_memory_mb = max_memory_mb
        
    def process_files_batch(self, file_list: List[str], batch_size: int = 10):
        """批量处理文件 - 内存控制"""
        # 主要功能：
        # 1. 按优先级分组处理
        # 2. 并行处理批次
        # 3. 内存使用监控
        # 4. 进度报告
        pass
    
    def _process_batch_parallel(self, file_batch: List[str]) -> Dict:
        """并行处理文件批次"""
        # 使用ThreadPoolExecutor进行并行处理
        # 超时控制和异常处理
        pass
```

#### 关键技术挑战与解决方案

**1. 表格结构复杂性处理**
- **合并单元格解析**：开发专门算法处理colspan/rowspan
- **多层表头识别**：智能识别和重构层次化表头
- **动态内容处理**：处理JavaScript生成的价格内容

**2. 中文文本特殊性**
- **分词优化**：结合jieba和自定义词典处理Azure术语
- **价格表达多样性**：支持"每小时¥1.23"、"¥123/月"等多种格式
- **语义理解**：识别"免费"、"包含"、"按需"等特殊定价

**3. 数据质量控制**
- **实时验证**：解析过程中实时验证数据合理性
- **置信度评估**：为每个提取的数据项评估可信度
- **异常标记**：自动标记异常价格和数据

### 🔍 Day 3: 定价表格专项解析

#### 核心功能设计

**1. 表格结构智能识别**
```python
class TableStructureAnalyzer:
    """表格结构分析器"""
    
    def analyze_table_structure(self, table_element) -> Dict:
        """分析表格结构"""
        return {
            'table_type': 'pricing_table',  # 表格类型
            'header_structure': {},         # 表头结构
            'data_pattern': {},            # 数据模式
            'complexity_level': 'complex', # 复杂度级别
            'parsing_strategy': 'multi_pass' # 推荐解析策略
        }
    
    def extract_table_headers(self, table) -> List[Dict]:
        """提取表头 - 处理多层表头和合并单元格"""
        # 核心算法：处理colspan/rowspan的复杂表头
        pass
    
    def identify_pricing_columns(self, headers: List[Dict]) -> List[int]:
        """识别定价相关列"""
        # 基于关键词和模式识别价格列
        pass
```

**2. 价格数据标准化处理**
```python
class PricingDataStandardizer:
    """定价数据标准化器"""
    
    def standardize_pricing_data(self, raw_pricing: List[Dict]) -> List[Dict]:
        """标准化定价数据"""
        return [
            {
                'service_tier': '',      # 服务等级
                'configuration': {},     # 配置信息
                'pricing': {},          # 价格信息
                'metadata': {},         # 元数据
                'confidence': 0.0       # 数据置信度
            }
        ]
    
    def validate_price_reasonability(self, price_data: Dict) -> Dict:
        """验证价格合理性"""
        # 价格范围检查、逻辑一致性验证
        pass
```

**3. 区域价格差异处理**
- **自动区域识别**：从表头或内容中识别"中国东部"、"中国北部"等区域
- **价格对比分析**：同一服务不同区域的价格差异分析
- **汇率处理**：处理可能的多币种价格

### 🔍 Day 4: 服务信息综合提取

#### 核心功能优化

**1. 服务基本信息提取**
```python
class ServiceInfoExtractor:
    """服务信息提取器"""
    
    def extract_service_profile(self, html_content: str) -> Dict:
        """提取服务档案"""
        return {
            'service_id': '',           # 服务标识
            'display_name': '',         # 显示名称
            'description': '',          # 服务描述
            'category': '',            # 服务分类
            'key_features': [],        # 关键特性
            'technical_specs': {},     # 技术规格
            'use_cases': [],          # 使用场景
            'related_services': [],    # 相关服务
            'pricing_summary': {},     # 定价摘要
            'extraction_metadata': {} # 提取元数据
        }
    
    def extract_technical_specifications(self, content: str) -> Dict:
        """提取技术规格"""
        # 识别性能指标、容量限制、技术参数
        pass
    
    def identify_use_cases(self, content: str) -> List[str]:
        """识别使用场景"""
        # 基于关键词和模式识别典型使用场景
        pass
```

**2. 内容质量评估与验证**
```python
class ContentQualityValidator:
    """内容质量验证器"""
    
    def assess_extraction_quality(self, extracted_data: Dict) -> Dict:
        """评估提取质量"""
        return {
            'completeness_score': 0.0,    # 完整性评分
            'accuracy_confidence': 0.0,   # 准确性置信度
            'consistency_check': {},      # 一致性检查
            'missing_fields': [],         # 缺失字段
            'quality_issues': []          # 质量问题
        }
```

### 🔍 Day 5: 集成测试与优化

#### 系统集成与质量控制

**1. 端到端测试框架**
```python
class ParsingIntegrationTester:
    """解析集成测试器"""
    
    def run_comprehensive_tests(self, test_files: List[str]) -> Dict:
        """运行全面测试"""
        return {
            'test_summary': {},
            'success_rate': 0.0,
            'performance_metrics': {},
            'error_analysis': {},
            'recommendations': []
        }
    
    def validate_against_samples(self, parsed_data: Dict, expected: Dict) -> Dict:
        """对比验证解析结果"""
        # 与预期结果对比，评估准确性
        pass
```

**2. 性能优化与调优**
- **内存使用优化**：大文件的流式处理
- **并发处理调优**：根据系统资源调整并发数
- **缓存策略**：重复模式的缓存优化

**3. 错误处理与恢复机制**
```python
class RobustParsingEngine:
    """容错性强的解析引擎"""
    
    def parse_with_fallback(self, html_content: str, file_info: Dict) -> ParseResult:
        """多策略容错解析"""
        # 策略链：复杂解析器 → 简单解析器 → 文本提取
        # 自动降级和错误恢复
        pass
```

### 📊 优化后的关键输出物

#### 1. 结构化数据输出格式
```json
{
  "extraction_metadata": {
    "extraction_timestamp": "2024-01-08T10:00:00Z",
    "parser_version": "v2.0",
    "strategy_used": "complex_table_parser",
    "confidence_score": 0.85,
    "processing_time_ms": 1500
  },
  "service_data": {
    "service_id": "virtual-machines",
    "pricing_tables": [
      {
        "table_id": "vm_pricing_china_east",
        "headers": [...],
        "standardized_data": [...],
        "raw_data": [...]
      }
    ],
    "service_info": {...},
    "quality_assessment": {...}
  }
}
```

#### 2. 解析日志与错误报告
```json
{
  "parsing_session": {
    "session_id": "session_20240108_001",
    "total_files": 500,
    "successful_parses": 425,
    "failed_parses": 75,
    "average_processing_time": 2.3
  },
  "error_analysis": {
    "encoding_errors": 12,
    "table_structure_errors": 28,
    "timeout_errors": 5,
    "other_errors": 30
  },
  "performance_metrics": {
    "files_per_minute": 26,
    "memory_peak_mb": 1850,
    "success_rate_by_service": {...}
  }
}
```

#### 3. 定价模式库
```json
{
  "discovered_patterns": {
    "vm_pricing_patterns": [
      {
        "pattern_id": "hourly_tiered",
        "description": "按小时分层定价",
        "regex_patterns": [...],
        "sample_texts": [...],
        "confidence": 0.9
      }
    ],
    "storage_pricing_patterns": [...],
    "database_pricing_patterns": [...]
  },
  "header_mappings": {
    "common_headers": {
      "虚拟机大小": "vm_size",
      "vCPU": "vcpu_count",
      "内存": "memory_gb",
      "中国东部价格": "price_china_east"
    }
  }
}
```

### ⚠️ 风险控制与应对策略

#### 1. 技术风险控制
**风险**: Azure定价表格结构过于复杂，解析失败率高
**应对**: 
- 建立多级解析策略，失败时自动降级
- 针对每种服务类型开发专门的解析规则
- 建立人工审查队列处理解析失败文件

#### 2. 数据质量风险
**风险**: 提取的价格数据不准确
**应对**:
- 实施多重验证机制：模式匹配 + 合理性检查 + 交叉验证
- 建立价格数据的置信度评估体系
- 对低置信度数据进行人工复核

#### 3. 性能风险控制
**风险**: 大量文件处理导致内存溢出或处理超时
**应对**:
- 实施智能批处理，动态调整批次大小
- 建立内存监控和自动清理机制
- 设置文件处理超时和重试机制

### ✅ 成功标准与验收条件

#### 量化指标优化
- **解析成功率**: >85% (原方案过于乐观，调整为更现实的目标)
- **定价数据准确率**: >90% (通过抽样验证)
- **处理性能**: 平均每文件 <5秒 (考虑Azure表格复杂性)
- **内存使用**: 峰值 <2GB (支持大规模处理)
- **置信度覆盖**: 100%文件都有置信度评分

#### 关键里程碑验收
- **Day 2**: 核心解析引擎通过100个样本文件测试
- **Day 3**: 定价表格解析达到80%成功率
- **Day 4**: 服务信息提取完整性达到85%
- **Day 5**: 端到端流程通过500个文件的完整测试

#### 质量验收标准
- **数据完整性**: 核心字段(服务名、价格、规格)提取率>90%
- **格式一致性**: 输出数据格式100%符合标准
- **错误处理**: 所有异常情况都有明确的错误类型和处理建议
- **性能稳定性**: 连续处理1000个文件无内存泄漏

## 阶段三：数据清洗与标准化 - 优化实施方案

### ⏰ 优化时间安排（4天完成）

**总体优化理念：**
- 建立Azure特色的数据质量控制体系
- 强化中文技术术语的智能处理
- 为混合RAG系统奠定高质量数据基础
- 实现大规模数据的高效处理和验证

```mermaid
gantt
    title 阶段三优化时间线
    dateFormat  YYYY-MM-DD
    section Day 1
    数据质量控制体系     :done, task1, 2024-01-09, 1d
    section Day 2
    Azure术语标准化      :active, task2, 2024-01-10, 1d
    section Day 3
    价格数据统一处理     :task3, 2024-01-11, 1d
    section Day 4
    质量验证与RAG准备    :task4, 2024-01-12, 1d
```

### 📐 核心技术架构重构

#### Azure特色数据质量控制
```python
class AzureDataQualityController:
    """Azure数据质量控制器 - 针对Azure服务特点优化"""
    
    def __init__(self):
        # Azure服务标准术语库
        self.azure_terminology = {
            'compute': ['虚拟机', 'VM', '计算实例', '云服务器'],
            'storage': ['存储', '磁盘', '数据湖', '归档'],
            'database': ['数据库', 'SQL', 'NoSQL', 'Cosmos'],
            'network': ['网络', 'VPN', 'CDN', '负载均衡']
        }
        
        # Azure区域标准化
        self.region_mapping = {
            '中国东部': 'china-east',
            '中国北部': 'china-north', 
            '中国东部2': 'china-east2'
        }
    
    def validate_service_data_completeness(self, service_data: Dict) -> Dict:
        """验证Azure服务数据完整性"""
        # 关键字段验证：
        # - service_id, display_name, pricing_data
        # - technical_specs, feature_list
        # - region_availability
        pass
    
    def check_cross_service_consistency(self, all_services: List[Dict]) -> Dict:
        """检查跨服务数据一致性"""
        # 验证：
        # - 相同服务不同页面的价格一致性
        # - 区域可用性的一致性
        # - 技术规格的逻辑一致性
        pass
    
    def standardize_azure_terminology(self, text: str) -> str:
        """标准化Azure术语"""
        # 统一术语表达：
        # - "虚拟机" vs "VM" vs "云服务器"
        # - "存储" vs "磁盘" vs "数据存储"
        pass
```

### 🔍 Day 1: 数据质量控制体系建立

#### 上午任务 (4小时): 质量评估体系

**目标**: 建立科学的数据质量评估和控制机制

**核心工作**:
- **质量评分标准建立**: 设计0-100分的量化评估体系
- **完整性检查规则**: 必填字段、数据格式、逻辑关系验证
- **异常数据自动检测**: 价格异常、描述缺失、格式错误识别
- **质量阈值设定**: 不同质量等级的阈值标准

**技术架构**:
```python
class DataQualityAssessor:
    """数据质量评估器"""
    
    def __init__(self):
        # 质量评估维度
        self.quality_dimensions = {
            'completeness': 0.3,    # 完整性权重30%
            'consistency': 0.25,    # 一致性权重25%
            'accuracy': 0.25,       # 准确性权重25%
            'validity': 0.2         # 有效性权重20%
        }
    
    def calculate_quality_score(self, service_data: Dict) -> Dict:
        """计算综合质量分数"""
        # 输出：
        # {
        #   "overall_score": 87.5,
        #   "dimension_scores": {...},
        #   "issues": [...],
        #   "recommendations": [...]
        # }
        pass
    
    def assess_completeness(self, data: Dict) -> float:
        """评估数据完整性"""
        pass
    
    def assess_consistency(self, data: Dict, reference_data: List[Dict]) -> float:
        """评估数据一致性"""
        pass
```

#### 下午任务 (4小时): 跨服务一致性验证

**目标**: 确保不同服务和页面间数据的逻辑一致性

**核心工作**:
- **价格一致性检查**: 同一服务在不同页面的价格对比
- **术语一致性验证**: 相同概念在不同服务中的表达统一
- **数据关联性检查**: 相关服务间的依赖关系验证
- **异常标记和处理**: 不一致数据的标记和处理策略

**关键技术挑战**:
- **大规模数据比对**: 500+服务的交叉验证性能优化
- **智能异常检测**: 区分真实差异和数据错误
- **置信度评估**: 为每个数据项建立可信度评分

### 🔍 Day 2: Azure术语标准化与中文处理

#### 全天任务 (8小时): 中文Azure术语智能处理

**目标**: 建立Azure中文术语的标准化处理体系

**技术架构**:
```python
class ChineseTextProcessor:
    """中文文本智能处理器 - Azure领域优化"""
    
    def __init__(self):
        # Azure专用词典
        self.azure_dict = {
            'technical_terms': ['vCPU', 'SSD', 'HDD', 'IOPS'],
            'pricing_terms': ['按需付费', '预留实例', '现用现付'],
            'service_names': ['应用服务', '函数计算', '容器实例']
        }
        
        # 术语标准化映射
        self.terminology_mapping = {
            "compute_services": {
                "variants": ["虚拟机", "VM", "云服务器", "计算实例"],
                "standard": "虚拟机",
                "english": "Virtual Machines"
            },
            "pricing_models": {
                "variants": ["按需付费", "现用现付", "即用即付"],
                "standard": "按需付费", 
                "english": "Pay-as-you-go"
            }
        }
    
    def process_service_descriptions(self, descriptions: List[str]) -> List[Dict]:
        """处理服务描述文本"""
        # 主要功能：
        # 1. 智能分词（考虑Azure术语）
        # 2. 关键特性提取
        # 3. 使用场景识别
        # 4. 技术规格提取
        # 5. 语义相似度计算
        pass
    
    def extract_key_features(self, text: str) -> List[str]:
        """提取关键特性"""
        pass
    
    def generate_service_summary(self, full_description: str) -> str:
        """生成服务摘要"""
        pass
    
    def calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """计算语义相似度"""
        pass
```

**核心工作内容**:
- **构建Azure中文术语库**: 收集和标准化所有Azure相关术语
- **实现智能术语替换**: 统一不同表达方式的相同概念
- **优化中文分词**: 针对Azure技术术语的jieba分词优化
- **语义相似度计算**: 建立服务间的语义关系网络
- **自动摘要生成**: 为长描述生成简洁、准确的摘要

### 🔍 Day 3: 价格数据统一处理

#### 上午任务 (4小时): 价格格式标准化

**目标**: 建立统一的价格数据格式和验证机制

**技术架构**:
```python
class PricingDataStandardizer:
    """价格数据智能标准化器"""
    
    def __init__(self):
        # 价格单位映射
        self.unit_standardization = {
            '小时': 'hour',
            '月': 'month', 
            '年': 'year',
            'GB/月': 'gb_per_month',
            '万次/月': 'ten_thousand_requests_per_month'
        }
        
        # 区域价格基准
        self.region_price_baseline = {
            'china-east': 1.0,    # 基准
            'china-north': 0.95,  # 通常便宜5%
            'china-east2': 1.02   # 通常贵2%
        }
    
    def standardize_pricing_structure(self, raw_pricing: List[Dict]) -> Dict:
        """标准化定价结构"""
        # 输出标准格式：
        # {
        #   "service_tier": "Standard_B2s",
        #   "pricing_by_region": {
        #     "china-east": {
        #       "hourly": 0.2016,
        #       "monthly": 147.168,
        #       "currency": "CNY"
        #     }
        #   },
        #   "pricing_metadata": {
        #     "last_updated": "2024-01-10",
        #     "confidence_score": 0.95
        #   }
        # }
        pass
    
    def validate_price_reasonability(self, pricing_data: Dict) -> Dict:
        """验证价格合理性"""
        # 验证规则：
        # 1. 价格范围检查（不能为负或过高）
        # 2. 区域价格差异合理性
        # 3. 时间单位换算一致性
        # 4. 同类服务价格对比
        pass
```

**核心工作**:
- **统一货币和精度**: 所有价格统一为CNY，保留4位小数
- **标准化计费周期**: 统一时间单位表达方式(hour/month/year)
- **区域价格整理**: 建立标准的区域价格对比体系
- **价格有效性验证**: 范围检查、逻辑一致性验证

#### 下午任务 (4小时): 复杂定价模式处理

**目标**: 处理Azure特有的复杂定价逻辑

**核心工作**:
- **阶梯定价标准化**: 识别和标准化用量阶梯定价
- **组合定价分析**: 处理多资源组合的复杂定价
- **折扣规则整理**: 标准化各种折扣和优惠规则
- **定价版本管理**: 建立价格历史和变更追踪

**技术难点**:
- **复杂定价逻辑**: Azure的阶梯定价、预留实例、批量折扣等
- **汇率和税费**: 处理可能存在的汇率转换和增值税
- **动态定价**: 根据使用量变化的动态定价模式

### 🔍 Day 4: 质量验证与RAG数据准备

#### 上午任务 (4小时): 最终质量验证

**目标**: 确保数据清洗和标准化的质量达标

**核心工作**:
- **端到端数据验证**: 从原始HTML到标准化数据的完整验证
- **抽样质量检查**: 人工抽样验证自动化处理结果的准确性
- **性能优化测试**: 大规模数据处理的性能调优和压力测试
- **异常处理验证**: 各种边界情况和异常的处理验证

#### 下午任务 (4小时): RAG系统数据准备

**目标**: 为混合智能RAG系统准备高质量的结构化数据

**技术架构**:
```python
# 为RAG系统准备的标准化数据结构
rag_ready_data = {
    "service_metadata": {
        "id": "virtual-machines",
        "category": "compute",
        "tags": ["infrastructure", "windows", "linux"],
        "complexity_level": "medium",
        "use_cases": ["web-hosting", "data-processing"],
        "query_popularity": "high"
    },
    "content_sections": {
        "overview": "标准化的服务概述",
        "features": ["关键特性列表"],
        "pricing": "标准化定价信息",
        "technical_specs": "技术规格"
    },
    "relationships": {
        "dependencies": ["storage", "networking"],
        "alternatives": ["app-service", "container-instances"],
        "complements": ["load-balancer", "monitor"]
    },
    "rag_optimization": {
        "embedding_priority": "high",
        "context_completeness": 0.95,
        "metadata_richness": 0.90
    }
}
```

**核心工作**:
- **元数据丰富化**: 为每个服务生成丰富的查询和过滤元数据
- **向量化预处理**: 为embedding做内容预处理和格式标准化
- **关系网络构建**: 建立服务间的依赖、替代、互补关系数据
- **RAG优化标记**: 为不同检索策略提供优化标记

### 📊 优化后的核心输出物

#### 1. 标准化数据格式
```json
{
  "standardization_metadata": {
    "version": "v1.0",
    "processing_date": "2024-01-12",
    "quality_score": 92.3,
    "coverage_rate": 98.7
  },
  "services": [
    {
      "service_id": "virtual-machines",
      "standardized_info": {
        "display_name": "虚拟机",
        "category": "计算服务",
        "description": "标准化描述文本",
        "key_features": ["弹性扩展", "多操作系统支持"],
        "technical_specs": {
          "cpu_types": ["Intel", "AMD"],
          "memory_range": "0.5GB-3.5TB",
          "storage_types": ["HDD", "SSD", "Premium SSD"]
        },
        "pricing": {
          "china-east": {
            "hourly_range": "0.05-50.00",
            "currency": "CNY"
          }
        }
      },
      "quality_metrics": {
        "completeness_score": 95.2,
        "consistency_score": 90.8,
        "accuracy_confidence": 88.5
      }
    }
  ]
}
```

#### 2. 数据质量报告
```json
{
  "quality_summary": {
    "total_services_processed": 487,
    "high_quality_services": 425,
    "medium_quality_services": 52,
    "low_quality_services": 10,
    "average_quality_score": 87.3
  },
  "quality_issues": {
    "missing_pricing": 15,
    "inconsistent_descriptions": 28,
    "invalid_specifications": 7
  },
  "improvement_actions": [
    "优先处理pricing缺失的15个服务",
    "统一description表达方式的28个不一致项",
    "验证和修正7个invalid_specifications"
  ]
}
```

#### 3. Azure术语标准化报告
```json
{
  "terminology_standardization": {
    "total_terms_processed": 1250,
    "standardized_mappings": 340,
    "conflict_resolutions": 28,
    "new_terms_discovered": 45
  },
  "semantic_relationships": {
    "service_similarity_matrix": "计算完成的语义相似度矩阵",
    "feature_clustering": "特性聚类结果",
    "use_case_mapping": "使用场景映射关系"
  }
}
```

### ⚠️ 风险控制与应对策略

#### 1. 数据质量风险控制
**风险**: 自动化清洗可能引入新的错误
**应对策略**:
- **多重验证机制**: 自动验证 + 规则检查 + 人工抽样
- **原始数据保留**: 完整保留原始数据，支持回溯和对比
- **质量阈值门控**: 低于阈值的数据进入人工审查队列
- **增量验证**: 支持数据更新时的增量验证

#### 2. 性能优化风险
**风险**: 大量数据处理可能导致性能瓶颈
**应对策略**:
- **分布式处理**: 按服务类别并行处理，提高效率
- **内存优化**: 流式处理大数据，避免OOM
- **进度监控**: 实时监控处理进度和异常恢复
- **资源弹性**: 根据数据量动态调整计算资源

#### 3. 一致性保障风险
**风险**: 不同来源数据的一致性难以保证
**应对策略**:
- **数据血缘追踪**: 记录每个数据的来源和处理过程
- **冲突检测和解决**: 智能检测数据冲突并提供解决方案
- **权威源优先级**: 建立数据源权威性优先级体系
- **一致性监控**: 持续监控数据一致性变化

### ✅ 成功标准与验收条件

#### 量化指标
- **数据完整性**: >95% 服务包含所有核心字段
- **一致性分数**: >90% 跨服务数据一致性检查通过
- **标准化覆盖率**: >98% 数据符合标准格式要求
- **质量评分**: 平均质量分 >85分
- **处理性能**: 500个服务完整处理 <2小时

#### 质量验收条件
- **价格数据准确性**: 抽样验证准确率 >95%
- **术语标准化**: 术语一致性覆盖率 >90%
- **RAG就绪度**: 100% 服务具备完整的RAG元数据
- **异常处理**: 所有识别的数据异常都有明确的处理记录

#### 关键里程碑
- **Day 1 End**: 质量控制体系建立完成，通过示例数据验证
- **Day 2 End**: Azure术语标准化完成，语义关系网络构建完成
- **Day 3 End**: 价格数据标准化完成，复杂定价逻辑处理验证
- **Day 4 End**: 最终质量验证通过，RAG数据准备完成

## 阶段四：数据建模与关系构建 - 优化实施方案

### ⏰ 优化时间安排（5天完成）

**总体优化理念：**
- 运用图算法和机器学习构建智能化的服务关系网络
- 建立Azure特色的多层级分类体系和复杂定价模型
- 为混合RAG系统提供高质量的结构化知识图谱
- 实现可验证、可解释的数据建模和关系发现

```mermaid
gantt
    title 阶段四优化时间线
    dateFormat  YYYY-MM-DD
    section Day 1
    Azure服务分类体系     :done, task1, 2024-01-13, 1d
    section Day 2
    智能关系网络构建      :active, task2, 2024-01-14, 1d
    section Day 3-4
    复杂定价模型重构      :task3, 2024-01-15, 2d
    section Day 5
    模型验证与优化        :task4, 2024-01-17, 1d
```

### 📐 核心技术架构重构

#### 智能化Azure服务分类体系
```python
class AzureServiceTaxonomyBuilder:
    """Azure服务智能分类体系构建器"""
    
    def __init__(self):
        # Azure官方服务分类标准
        self.official_categories = {
            'compute': ['虚拟机', '应用服务', '函数', '容器实例'],
            'storage': ['存储账户', 'Cosmos DB', 'SQL数据库'],
            'networking': ['虚拟网络', '负载均衡器', 'CDN'],
            'analytics': ['数据工厂', '流分析', 'Power BI'],
            'ai_ml': ['认知服务', '机器学习', '机器人服务'],
            'security': ['密钥保管库', '安全中心', 'Azure AD']
        }
        
        # 多维度分类特征
        self.classification_features = {
            'technical_characteristics': ['计算密集型', '存储密集型', '网络密集型'],
            'usage_patterns': ['实时处理', '批量处理', '交互式'],
            'deployment_models': ['IaaS', 'PaaS', 'SaaS'],
            'pricing_models': ['按需', '预留', '混合']
        }
    
    def build_hierarchical_taxonomy(self, services_data: List[Dict]) -> Dict:
        """构建层次化分类体系"""
        # 主要功能：
        # 1. 基于服务描述的自动分类
        # 2. 多维度特征提取和聚类
        # 3. 层次化分类树构建
        # 4. 分类准确性验证
        pass
    
    def extract_service_features(self, service_data: Dict) -> Dict:
        """提取服务多维度特征"""
        pass
    
    def auto_classify_service(self, service_features: Dict) -> Dict:
        """基于特征的自动分类"""
        pass
    
    def validate_classification_accuracy(self, classifications: Dict) -> Dict:
        """验证分类准确性"""
        pass
```

### 🔍 Day 1: Azure服务智能分类体系构建

#### 全天任务 (8小时): 多维度分类体系设计

**目标**: 构建科学、实用的Azure服务分类体系

**技术架构**:
```python
class MultiDimensionalClassifier:
    """多维度服务分类器"""
    
    def __init__(self):
        # 分类维度定义
        self.classification_dimensions = {
            'functional': {
                'compute': ['计算', '处理', '运行'],
                'storage': ['存储', '保存', '数据库'],
                'network': ['网络', '连接', '传输'],
                'security': ['安全', '认证', '加密'],
                'analytics': ['分析', '智能', '洞察']
            },
            'technical': {
                'infrastructure': ['基础设施', '硬件', '虚拟化'],
                'platform': ['平台', '框架', '运行时'],
                'software': ['软件', '应用', '服务']
            },
            'business': {
                'cost_model': ['按需', '预留', '固定'],
                'complexity': ['简单', '中等', '复杂'],
                'popularity': ['热门', '常用', '专业']
            }
        }
    
    def classify_service_multi_dimensional(self, service_data: Dict) -> Dict:
        """多维度服务分类"""
        # 输出结构：
        # {
        #   "primary_category": "compute",
        #   "secondary_categories": ["infrastructure", "按需"],
        #   "classification_confidence": 0.92,
        #   "feature_scores": {...},
        #   "classification_reasoning": "基于关键词和技术特征分析"
        # }
        pass
    
    def build_classification_tree(self, all_services: List[Dict]) -> Dict:
        """构建分类决策树"""
        pass
    
    def generate_service_tags(self, service_data: Dict) -> List[str]:
        """生成服务标签"""
        pass
```

**核心工作内容**:
- **多维度特征提取**: 从服务描述中提取功能、技术、业务维度特征
- **机器学习分类**: 使用监督学习训练分类模型
- **层次化分类树**: 构建可解释的层次化分类结构
- **标签体系生成**: 为每个服务生成丰富的标签集合

### 🔍 Day 2: 智能服务关系网络构建

#### 全天任务 (8小时): 图算法驱动的关系发现

**目标**: 构建智能化的Azure服务关系知识图谱

**技术架构**:
```python
import networkx as nx
from sklearn.metrics.pairwise import cosine_similarity

class ServiceRelationshipGraph:
    """服务关系图谱构建器"""
    
    def __init__(self):
        # 关系类型定义
        self.relationship_types = {
            'dependency': {
                'strength': ['strong', 'medium', 'weak'],
                'direction': ['bidirectional', 'unidirectional']
            },
            'complementary': {
                'frequency': ['often', 'sometimes', 'rarely'],
                'use_case': ['web_app', 'data_analytics', 'ml_pipeline']
            },
            'alternative': {
                'similarity': ['high', 'medium', 'low'],
                'trade_offs': ['cost', 'performance', 'complexity']
            },
            'upgrade_path': {
                'direction': ['forward', 'backward'],
                'migration_complexity': ['easy', 'medium', 'hard']
            }
        }
        
        # 图谱存储
        self.service_graph = nx.MultiDiGraph()
    
    def build_service_graph(self, services_data: List[Dict]) -> nx.MultiDiGraph:
        """构建服务关系图谱"""
        # 主要步骤：
        # 1. 服务节点创建和属性设置
        # 2. 依赖关系自动发现
        # 3. 互补关系挖掘
        # 4. 竞争关系识别
        # 5. 升级路径映射
        # 6. 关系权重计算
        pass
    
    def discover_dependencies(self, services_data: List[Dict]) -> List[Dict]:
        """发现服务依赖关系"""
        # 基于以下规则发现依赖：
        # - 技术文档中的明确依赖描述
        # - 配置示例中的服务组合
        # - 定价捆绑中的服务关联
        pass
    
    def identify_complementary_services(self, services_data: List[Dict]) -> List[Dict]:
        """识别互补服务"""
        # 基于以下数据挖掘互补关系：
        # - 使用场景描述的共现分析
        # - 技术架构中的常见组合
        # - 最佳实践文档中的推荐组合
        pass
    
    def calculate_service_similarity(self, service1: Dict, service2: Dict) -> float:
        """计算服务相似度"""
        pass
    
    def detect_competitive_relationships(self, services_data: List[Dict]) -> List[Dict]:
        """检测竞争关系"""
        pass
    
    def map_upgrade_paths(self, services_data: List[Dict]) -> List[Dict]:
        """映射升级路径"""
        pass
    
    def calculate_relationship_weights(self, graph: nx.MultiDiGraph) -> nx.MultiDiGraph:
        """计算关系权重"""
        pass
    
    def generate_recommendation_matrix(self, graph: nx.MultiDiGraph) -> Dict:
        """生成推荐矩阵"""
        # 为RAG系统和用户推荐提供基础数据
        pass
```

**核心算法应用**:
- **图算法**: PageRank计算服务重要性，社区发现识别服务集群
- **NLP算法**: 文本相似度计算，关键词共现分析
- **机器学习**: 无监督聚类发现隐含关系模式
- **网络分析**: 中心性分析，路径分析，影响力传播

### 🔍 Day 3-4: 复杂定价模型重构

#### Day 3: 定价引擎架构设计

**目标**: 设计可扩展的复杂定价计算引擎

**技术架构**:
```python
from abc import ABC, abstractmethod
from typing import Dict, List, Optional
from decimal import Decimal
from enum import Enum

class PricingModelType(Enum):
    PAY_AS_YOU_GO = "payg"
    RESERVED_INSTANCE = "reserved"
    SPOT_INSTANCE = "spot"
    HYBRID = "hybrid"
    TIERED = "tiered"
    BUNDLED = "bundled"

class PricingEngine:
    """Azure复杂定价引擎"""
    
    def __init__(self):
        # 定价模型注册表
        self.pricing_models = {
            PricingModelType.PAY_AS_YOU_GO: PayAsYouGoPricingModel(),
            PricingModelType.RESERVED_INSTANCE: ReservedInstancePricingModel(),
            PricingModelType.TIERED: TieredPricingModel(),
            PricingModelType.BUNDLED: BundledPricingModel()
        }
        
        # 折扣规则引擎
        self.discount_engine = DiscountRulesEngine()
        
        # 区域价格调整
        self.regional_adjustments = {
            'china-east': 1.0,
            'china-north': 0.98,
            'china-east2': 1.05
        }
    
    def calculate_total_cost(self, 
                           service_config: Dict, 
                           usage_parameters: Dict,
                           pricing_preferences: Dict) -> Dict:
        """计算总成本 - 支持复杂定价逻辑"""
        # 主要计算流程：
        # 1. 识别适用的定价模型
        # 2. 基础成本计算
        # 3. 阶梯定价应用
        # 4. 折扣规则应用
        # 5. 区域调整
        # 6. 税费计算
        # 7. 成本明细生成
        pass
    
    def optimize_pricing_strategy(self, 
                                usage_pattern: Dict, 
                                constraints: Dict) -> Dict:
        """优化定价策略"""
        # 为用户推荐最优的定价组合
        pass
```

#### Day 4: 定价模型具体实现

**核心定价模型实现**:
```python
class TieredPricingModel(ABC):
    """阶梯定价模型"""
    
    def calculate_tiered_cost(self, 
                            usage_amount: Decimal, 
                            tier_rules: List[Dict]) -> Dict:
        """计算阶梯定价成本"""
        # 处理复杂的用量阶梯：
        # - 第一层: 0-100 GB, ¥0.10/GB
        # - 第二层: 100-1TB, ¥0.08/GB  
        # - 第三层: >1TB, ¥0.06/GB
        pass

class ReservedInstancePricingModel(ABC):
    """预留实例定价模型"""
    
    def calculate_reserved_savings(self, 
                                 payg_cost: Decimal,
                                 reserved_term: int,
                                 commitment_level: float) -> Dict:
        """计算预留实例节省"""
        # 计算1年/3年预留的节省额度
        pass

class BundledPricingModel(ABC):
    """捆绑定价模型"""
    
    def calculate_bundle_discount(self, 
                                services: List[str],
                                usage_params: Dict) -> Dict:
        """计算捆绑折扣"""
        # 处理多服务组合的捆绑优惠
        pass
```

### 🔍 Day 5: 模型验证与RAG数据准备

#### 上午任务 (4小时): 模型准确性验证

**目标**: 验证分类和关系模型的准确性和有效性

**验证框架**:
```python
class ModelValidationFramework:
    """模型验证框架"""
    
    def __init__(self):
        pass
    
    def validate_classification_accuracy(self, 
                                       classifications: Dict,
                                       ground_truth: Dict) -> Dict:
        """验证分类准确性"""
        # 验证指标：
        # - 分类准确率
        # - 精确率和召回率
        # - F1分数
        # - 混淆矩阵分析
        pass
    
    def validate_relationship_quality(self, 
                                    relationship_graph: nx.MultiDiGraph,
                                    validation_samples: List[Dict]) -> Dict:
        """验证关系质量"""
        # 验证维度：
        # - 关系发现的准确性
        # - 关系权重的合理性
        # - 推荐结果的相关性
        pass
    
    def validate_pricing_accuracy(self, 
                                pricing_results: Dict,
                                official_pricing: Dict) -> Dict:
        """验证定价准确性"""
        # 对比官方定价，验证计算准确性
        pass
```

#### 下午任务 (4小时): RAG系统数据结构优化

**目标**: 为混合RAG系统准备优化的知识结构

**RAG优化数据结构**:
```python
# 为混合RAG系统优化的数据结构
optimized_rag_data = {
    "knowledge_graph": {
        "services": {
            "virtual-machines": {
                "metadata": {
                    "category": "compute",
                    "subcategories": ["infrastructure", "按需"],
                    "complexity_level": "medium",
                    "popularity_score": 0.95,
                    "query_keywords": ["虚拟机", "VM", "云服务器", "计算实例"]
                },
                "relationships": {
                    "strong_dependencies": ["storage", "networking"],
                    "common_combinations": ["load-balancer", "monitor"],
                    "alternatives": ["app-service", "container-instances"],
                    "upgrade_paths": ["dedicated-hosts", "vmss"]
                },
                "pricing_intelligence": {
                    "cost_optimization_tips": [...],
                    "pricing_models": ["payg", "reserved", "spot"],
                    "cost_drivers": ["cpu", "memory", "storage"]
                }
            }
        },
        "relationship_matrix": {
            "dependency_strength": {...},
            "recommendation_scores": {...},
            "similarity_matrix": {...}
        }
    },
    "search_optimization": {
        "semantic_clusters": {
            "compute_cluster": ["virtual-machines", "app-service", "functions"],
            "data_cluster": ["sql-database", "cosmos-db", "storage"],
            "ai_cluster": ["cognitive-services", "machine-learning"]
        },
        "query_routing_rules": {
            "pricing_queries": "route_to_pricing_engine",
            "comparison_queries": "route_to_relationship_graph",
            "recommendation_queries": "route_to_ml_recommender"
        }
    }
}
```

### 📊 优化后的核心输出物

#### 1. Azure服务分类体系
```json
{
  "classification_metadata": {
    "version": "v2.0",
    "classification_date": "2024-01-17",
    "accuracy_score": 94.3,
    "coverage_rate": 99.2
  },
  "hierarchical_taxonomy": {
    "level_1": {
      "compute": {
        "services_count": 12,
        "subcategories": ["virtual-machines", "containers", "serverless"]
      },
      "storage": {
        "services_count": 8,
        "subcategories": ["blob", "files", "databases"]
      }
    },
    "level_2": {
      "compute.virtual-machines": {
        "services": ["虚拟机", "专用主机", "虚拟机规模集"],
        "characteristics": ["IaaS", "按需", "可扩展"]
      }
    }
  },
  "classification_features": {
    "feature_importance": {
      "functionality_keywords": 0.35,
      "technical_specifications": 0.25,
      "pricing_model": 0.20,
      "usage_patterns": 0.20
    }
  }
}
```

#### 2. 服务关系知识图谱
```json
{
  "graph_metadata": {
    "nodes_count": 487,
    "edges_count": 1247,
    "relationship_types": 4,
    "graph_density": 0.052
  },
  "relationship_statistics": {
    "dependency_relationships": 342,
    "complementary_relationships": 498,
    "competitive_relationships": 156,
    "upgrade_relationships": 251
  },
  "centrality_analysis": {
    "most_central_services": [
      {"service": "virtual-machines", "pagerank": 0.087},
      {"service": "storage", "pagerank": 0.073},
      {"service": "networking", "pagerank": 0.065}
    ]
  },
  "community_detection": {
    "clusters": [
      {
        "cluster_id": "web_stack",
        "services": ["app-service", "sql-database", "cdn"],
        "cluster_strength": 0.82
      }
    ]
  }
}
```

#### 3. 复杂定价模型库
```json
{
  "pricing_models": {
    "virtual_machines": {
      "base_pricing": {
        "pay_as_you_go": {
          "Standard_B1s": {"hourly": 0.0504, "monthly": 36.79},
          "Standard_B2s": {"hourly": 0.2016, "monthly": 147.17}
        }
      },
      "reserved_pricing": {
        "1_year": {"discount_rate": 0.31, "upfront_required": false},
        "3_year": {"discount_rate": 0.49, "upfront_required": true}
      },
      "tiered_discounts": [
        {"threshold": 100, "discount": 0.05},
        {"threshold": 1000, "discount": 0.10}
      ]
    }
  },
  "pricing_optimization": {
    "cost_calculators": ["vm_rightsizing", "reserved_optimizer"],
    "savings_opportunities": ["unused_resources", "rightsizing", "reserved_instances"]
  }
}
```

### ⚠️ 风险控制与应对策略

#### 1. 模型准确性风险
**风险**: 自动分类和关系发现可能存在错误
**应对策略**:
- **多重验证**: 机器学习 + 规则引擎 + 人工验证
- **置信度评估**: 为每个分类和关系提供置信度分数
- **持续学习**: 建立反馈机制，持续优化模型
- **专家验证**: 关键关系由Azure专家验证

#### 2. 图算法性能风险
**风险**: 大规模服务关系图的计算性能问题
**应对策略**:
- **分层计算**: 按服务类别分层计算关系
- **缓存优化**: 缓存常用的关系查询结果
- **增量更新**: 支持关系图的增量更新
- **分布式计算**: 使用分布式图计算框架

#### 3. 定价模型复杂性风险
**风险**: Azure定价规则变化导致模型失效
**应对策略**:
- **模块化设计**: 定价规则模块化，便于快速更新
- **版本管理**: 建立定价模型的版本管理机制
- **监控预警**: 监控官方定价变化，及时更新模型
- **降级策略**: 复杂计算失败时的简化计算方案

### ✅ 成功标准与验收条件

#### 量化指标
- **分类准确率**: >94% 服务分类准确性
- **关系发现精度**: >90% 关系识别准确率
- **定价计算准确性**: >98% 定价计算准确率
- **图算法性能**: 500服务关系计算 <30秒
- **RAG数据完整性**: 100% 服务具备完整的知识图谱数据

#### 质量验收条件
- **分类体系完整性**: 所有服务都有明确的多维度分类
- **关系网络连通性**: 图谱连通性 >85%
- **定价模型覆盖**: 支持所有主要定价模式
- **知识图谱质量**: 节点和边的属性完整性 >95%

#### 关键里程碑
- **Day 1 End**: 分类体系构建完成，分类准确率验证通过
- **Day 2 End**: 服务关系图谱构建完成，关系质量验证通过
- **Day 4 End**: 复杂定价模型实现完成，计算准确性验证通过
- **Day 5 End**: 模型验证和RAG数据准备完成，整体质量达标

## 阶段五：计算器逻辑重建

### 5.1 计算引擎架构
**目标**：设计可扩展的定价计算引擎架构

**实施内容**：
- **计算器框架设计**：模块化的计算器架构
- **规则引擎实现**：支持复杂业务规则的引擎
- **参数验证器**：输入参数的验证和约束检查
- **计算结果格式化**：统一的计算结果输出格式
- **计算过程追踪**：详细的计算步骤记录

**技术要求**：
- 设计高度可配置的计算框架
- 实现计算过程的完整追踪
- 建立计算结果的缓存机制

**输出物**：
- `calculator_framework.py`：计算器框架
- `calculation_rules.json`：计算规则配置
- `validator_schemas.json`：参数验证模式

### 5.2 复杂定价逻辑实现
**目标**：实现Azure特有的复杂定价计算逻辑

**实施内容**：
- **阶梯计费算法**：实现用量阶梯的精确计算
- **时间相关计费**：处理按小时、按月的不同计费周期
- **资源组合计费**：多资源组合使用的计费逻辑
- **优惠策略计算**：各种折扣和优惠的叠加计算
- **税费和汇率处理**：税费计算和汇率转换

**技术要求**：
- 实现高精度的浮点数计算
- 处理复杂的时间计算逻辑
- 建立完整的测试用例覆盖

**输出物**：
- `pricing_algorithms.py`：核心定价算法
- `calculation_examples.json`：计算示例集
- `test_cases.json`：测试用例库

### 5.3 计算结果展示
**目标**：设计清晰的计算过程和结果展示

**实施内容**：
- **计算步骤分解**：将复杂计算分解为清晰步骤
- **费用明细展示**：详细的费用构成和明细
- **可视化图表**：费用分布和趋势的图表展示
- **对比分析功能**：不同配置方案的对比
- **报告生成功能**：可导出的计算报告

**技术要求**：
- 实现清晰的数据结构设计
- 建立标准的展示模板
- 支持多种输出格式

**输出物**：
- `display_templates.json`：展示模板
- `chart_configs.json`：图表配置
- `report_generators.py`：报告生成器

## 阶段六：混合智能RAG数据准备

### 6.1 多层级RAG架构设计
**目标**：设计兼顾效率和质量的混合RAG系统，平衡信息完整性与成本控制

**实施内容**：
- **metadata驱动过滤层**：通过丰富metadata实现精准的预过滤
- **智能embedding层**：基于产品/服务维度的有意义向量化
- **rerank优化层**：对候选结果进行语义重排序
- **上下文组装层**：智能组装完整上下文，避免信息碎片化
- **成本优化策略**：动态选择检索策略，平衡质量与成本

**技术要求**：
- 设计多层级的检索架构，支持不同复杂度的查询
- 实现智能的策略选择算法，根据查询类型选择最优方案
- 建立成本监控和优化机制
- 支持检索策略的动态调整

**输出物**：
- `hybrid_rag_architecture.json`：混合RAG架构设计
- `retrieval_strategies.json`：检索策略配置
- `cost_optimization.json`：成本优化策略

### 6.2 智能metadata索引体系
**目标**：构建高效的metadata过滤系统，实现精准的预检索

**实施内容**：
- **多维度metadata设计**：服务类型、价格区间、使用场景、技术特征等
- **层次化分类体系**：构建服务的多层级分类和标签系统
- **查询意图识别**：自动识别用户查询的意图和范围
- **智能过滤算法**：基于metadata的快速过滤和范围缩小
- **相关性预评估**：在embedding前进行相关性初步评估

**技术要求**：
- 建立结构化的metadata索引系统
- 实现高效的多维度查询和过滤
- 设计智能的查询意图识别算法
- 建立metadata的自动更新和维护机制

**输出物**：
- `metadata_index_system.json`：metadata索引系统
- `classification_hierarchy.json`：分类层次结构
- `intent_recognition_rules.json`：意图识别规则
- `filter_algorithms.py`：过滤算法实现

### 6.3 产品维度智能向量化
**目标**：实现有意义的向量化策略，避免盲目切片造成的信息损耗

**实施内容**：
- **产品级完整embedding**：以单个Azure服务为单位进行向量化
- **功能维度embedding**：针对服务的核心功能进行专门向量化
- **定价维度embedding**：对定价相关信息进行专门处理
- **场景维度embedding**：基于使用场景的内容向量化
- **关联关系embedding**：服务间依赖和推荐关系的向量化

**技术要求**：
- 使用先进的embedding模型（如text-embedding-3-large）
- 实现多维度的向量化策略
- 建立向量质量评估和优化机制
- 支持增量更新和版本管理

**输出物**：
- `product_embeddings.h5`：产品级向量数据
- `feature_embeddings.h5`：功能维度向量
- `pricing_embeddings.h5`：定价相关向量
- `relationship_embeddings.h5`：关联关系向量

### 6.4 混合检索策略引擎
**目标**：根据查询复杂度和精度要求，智能选择最优检索策略

**实施内容**：
- **快速检索模式**：简单查询使用metadata+embedding的轻量级检索
- **精准检索模式**：复杂查询使用多层级检索+rerank优化
- **完整上下文模式**：高精度需求使用大上下文窗口完整检索
- **成本控制策略**：根据查询重要性和用户等级选择策略
- **性能监控优化**：实时监控不同策略的效果和成本

**技术要求**：
- 实现多种检索策略的无缝切换
- 建立查询复杂度的自动评估
- 实现成本和质量的动态平衡
- 建立策略效果的持续优化机制

**输出物**：
- `retrieval_engine.py`：混合检索引擎
- `strategy_selector.py`：策略选择器
- `performance_monitor.py`：性能监控器
- `cost_tracker.json`：成本追踪配置

### 6.5 上下文智能组装
**目标**：将检索到的内容片段智能组装成完整、连贯的上下文

**实施内容**：
- **内容关联性分析**：分析检索内容间的逻辑关系
- **上下文补全算法**：自动补充缺失的关联信息
- **信息去重优化**：智能去除重复和冗余信息
- **逻辑顺序重组**：按照合理的逻辑顺序重新组织内容
- **完整性验证**：确保组装后内容的完整性和准确性

**技术要求**：
- 实现智能的内容关联分析算法
- 建立上下文补全的规则引擎
- 实现高效的去重和排序算法
- 建立内容完整性的验证机制

**输出物**：
- `context_assembler.py`：上下文组装器
- `content_relationship.json`：内容关联规则
- `deduplication_rules.json`：去重规则配置
- `completeness_validator.py`：完整性验证器

## 阶段七：数据验证与质量保证

### 7.1 RAG数据质量验证
**目标**：确保RAG系统数据的高质量和AI助手的准确性

**实施内容**：
- **完整性验证**：验证服务文档的完整性和自包含性
- **语义一致性检查**：确保文档内容的语义连贯性
- **metadata准确性验证**：验证metadata的准确性和完整性
- **分区效率测试**：测试分区策略的效率和准确性
- **缓存命中率测试**：验证缓存策略的有效性

**技术要求**：
- 建立RAG专门的质量评估体系
- 实现自动化的语义一致性检查
- 建立缓存效率的监控机制

**输出物**：
- `rag_quality_report.json`：RAG数据质量报告
- `semantic_validation.json`：语义验证结果
- `cache_efficiency.json`：缓存效率报告

### 7.2 AI助手功能验证
**目标**：验证AI助手的定价计算和问答准确性

**实施内容**：
- **定价计算验证**：验证复杂定价计算的准确性
- **问答质量测试**：测试AI助手对Azure服务问题的回答质量
- **计算过程展示验证**：验证计算过程可视化的准确性
- **边界情况测试**：测试极端和异常情况的处理
- **用户体验测试**：测试AI助手的易用性和友好性

**技术要求**：
- 建立标准的AI助手测试用例库
- 实现自动化的准确性验证
- 建立用户体验的评估标准

**输出物**：
- `ai_assistant_tests.json`：AI助手测试用例
- `accuracy_validation.json`：准确性验证报告
- `ux_evaluation.json`：用户体验评估

### 7.3 性能与缓存优化验证
**目标**：验证新一代RAG系统的性能优势

**实施内容**：
- **响应时间测试**：测试AI助手的响应速度
- **token使用效率**：验证输入输出token比例的优化效果
- **缓存命中率监控**：监控deepseek缓存的实际命中率
- **并发处理能力**：测试系统的并发查询处理能力
- **资源使用优化**：优化GPU和内存的使用效率

**技术要求**：
- 建立全面的性能监控体系
- 实现token使用的精确统计
- 建立缓存策略的动态优化机制

**输出物**：
- `performance_metrics.json`：性能指标报告
- `token_efficiency.json`：token使用效率报告
- `cache_optimization.json`：缓存优化建议

## 阶段八：多目标数据导出

### 8.1 混合RAG系统数据导出
**目标**：导出支持混合智能RAG系统的完整数据包

**实施内容**：
- **metadata索引包**：导出多维度metadata索引和过滤规则
- **多层级向量包**：导出产品级、功能级、场景级等不同维度的向量数据
- **传统RAG数据包**：导出embedding向量、rerank模型配置、检索索引
- **大上下文数据包**：导出完整服务文档、分区配置、上下文优化
- **混合策略配置包**：导出检索策略选择、成本控制、性能优化配置

**技术要求**：
- 支持多种向量存储格式（faiss、chromadb、qdrant）
- 实现向量数据的版本管理和增量更新
- 建立不同检索策略的配置管理
- 支持成本和性能的监控配置

**输出物**：
- `hybrid_rag_complete_package.tar.gz`：完整混合RAG数据包
- `metadata_index.json`：metadata索引数据
- `vector_embeddings/`：多维度向量数据目录
- `traditional_rag_config.json`：传统RAG配置
- `large_context_config.json`：大上下文配置
- `hybrid_strategy_config.json`：混合策略配置

### 8.2 CMS系统数据导出
**目标**：导出CMS内容管理系统专用的标准化数据

**实施内容**：
- **标准化JSON格式**：导出符合CMS标准的JSON数据
- **关系型数据导出**：导出SQL建表脚本和数据
- **CSV批量导入格式**：导出便于Excel操作的CSV文件
- **图片资源清单**：导出所有图片资源的路径和metadata
- **版本控制数据**：导出支持版本管理的数据格式

**技术要求**：
- 遵循CMS的数据格式标准
- 实现多种格式的自动转换
- 建立数据一致性验证机制

**输出物**：
- `cms_data_package.json`：CMS数据包
- `database_schema.sql`：数据库建表脚本
- `bulk_import.csv`：批量导入CSV文件
- `media_manifest.json`：媒体资源清单

### 8.3 API接口数据导出
**目标**：导出API接口和第三方集成专用的数据

**实施内容**：
- **RESTful API数据格式**：导出标准的API响应格式
- **OpenAPI规范文档**：生成完整的API文档
- **定价计算API**：导出定价计算的API接口数据
- **服务查询API**：导出服务信息查询的API数据
- **实时更新接口**：导出支持实时数据更新的接口格式

**技术要求**：
- 遵循REST API设计最佳实践
- 实现API版本管理
- 建立API数据的验证和测试机制

**输出物**：
- `api_data_package.json`：API数据包
- `openapi_spec.yaml`：OpenAPI规范文档
- `api_examples.json`：API调用示例
- `api_tests.json`：API测试用例

### 8.4 数据文档与工具导出
**目标**：导出完整的使用文档和辅助工具

**实施内容**：
- **RAG部署指南**：AI智能助手的部署和配置指南
- **CMS导入工具**：自动化的CMS数据导入工具
- **数据更新工具**：支持增量更新的数据同步工具
- **监控仪表板**：RAG系统性能监控的仪表板
- **故障排除手册**：常见问题和解决方案手册

**技术要求**：
- 编写详细的技术文档
- 开发用户友好的工具界面
- 建立完整的帮助系统

**输出物**：
- `rag_deployment_guide.md`：RAG部署指南
- `cms_import_tool.py`：CMS导入工具
- `data_sync_tool.py`：数据同步工具
- `monitoring_dashboard.html`：监控仪表板
- `troubleshooting_guide.md`：故障排除指南

## 项目管控与质量保证

### 里程碑设置
- **M1 (Week 1)**：完成HTML文件分析和解析引擎
- **M2 (Week 2)**：完成核心数据提取和清洗
- **M3 (Week 3)**：完成数据建模和关系构建
- **M4 (Week 4)**：完成定价计算器逻辑重建
- **M5 (Week 5-6)**：完成混合智能RAG数据准备（包含传统RAG + 大上下文）
- **M6 (Week 7)**：完成验证和多目标数据导出

### 风险控制
- **技术风险**：HTML结构复杂导致解析困难
  - *缓解措施*：分类处理，针对不同结构开发专门解析器
- **数据风险**：HTML中的数据不完整或格式不一致
  - *缓解措施*：多重验证，异常数据标记和人工复核
- **性能风险**：大量HTML文件处理性能不足
  - *缓解措施*：并行处理，分批加载，内存优化

### 质量标准
- **数据准确率**：关键定价数据准确率 > 95%
- **覆盖率**：核心服务覆盖率 > 90%
- **性能指标**：单服务处理时间 < 30秒
- **代码质量**：代码覆盖率 > 80%，文档完备率 > 90%

## 交付物清单

### 核心交付物
1. **源代码包**：完整的Python项目代码
2. **处理数据包**：所有处理后的结构化数据
3. **数据库文件**：包含所有数据的SQLite数据库
4. **导出工具包**：CMS导入工具和脚本

### 文档交付物
1. **技术文档**：系统架构和实现文档
2. **数据文档**：数据字典和使用指南
3. **操作手册**：部署和维护操作手册
4. **测试报告**：完整的测试和验证报告

### 配置交付物
1. **配置文件**：所有系统配置和参数
2. **部署脚本**：自动化部署和安装脚本
3. **监控配置**：性能监控和日志配置
4. **数据模板**：标准数据格式和模板

## 后续扩展规划

### 短期扩展（3个月内）
- **增量更新机制**：支持定期自动更新
- **多语言支持**：扩展英文版本的处理
- **高级计算器**：支持更复杂的组合定价

### 中期扩展（6个月内）
- **竞品比较**：集成其他云服务商的价格比较
- **成本优化建议**：基于使用模式的成本优化
- **预测分析**：价格趋势预测和分析

### 长期扩展（1年内）
- **智能推荐**：基于AI的服务推荐
- **实时同步**：与Azure官方API的实时同步
- **高级分析**：成本分析和优化的高级功能

## 特别说明：生产环境HTML文件处理

### 实际文件路径结构
**根据项目实际情况，HTML源文件已存放在：**
```
项目根目录/
└── current_prod_html/
    └── zh-cn/
        └── pricing/
            └── details/
                ├── {service1}/           # 如：virtual-machines
                ├── {service2}/           # 如：storage  
                ├── {service3}/           # 如：sql-database
                └── ...                   # 其他Azure服务
```

## 混合RAG实施策略详解

### 成本与效率平衡考虑

**查询类型分级策略：**
- **L0-FAQ快速响应**：预设问答，直接匹配（成本：0）
- **L1-基础信息查询**：metadata过滤 + 关键词匹配（成本：极低）
- **L2-产品对比查询**：embedding检索 + 简单排序（成本：低）
- **L3-复杂定价计算**：rerank优化 + 规则引擎（成本：中等）
- **L4-深度分析查询**：大上下文LLM + 完整推理（成本：高）

**动态策略选择算法：**
```
查询复杂度评估 → 用户等级检查 → 成本预算控制 → 策略路由选择
```

**成本控制机制：**
1. **Token使用预估**：查询前预估token消耗，超预算降级处理
2. **缓存优先策略**：优先使用cached结果，相似查询复用
3. **批量处理优化**：相关查询批量处理，减少API调用次数
4. **用户配额管理**：不同用户等级设置不同的L4查询配额

### 数据组织优化策略

**产品维度embedding策略：**
- **完整产品文档**：每个Azure服务一个完整embedding（避免切片损失）
- **功能模块embedding**：核心功能、定价、案例等分别embedding
- **关联服务embedding**：服务间依赖关系的专门向量化
- **用户场景embedding**：基于典型使用场景的内容组织

**metadata丰富化设计：**
```json
{
  "service_id": "virtual-machines",
  "categories": ["compute", "infrastructure"],
  "price_range": "low-medium-high",
  "complexity": "medium", 
  "use_cases": ["web-hosting", "data-processing"],
  "dependencies": ["storage", "networking"],
  "update_frequency": "monthly",
  "query_popularity": "high"
}
```

**检索效率优化：**
1. **预计算相似度**：热门服务间的相似度预计算缓存
2. **分层索引结构**：类别→服务→功能的层次化索引
3. **智能预加载**：根据查询模式预加载相关内容
4. **结果缓存策略**：基于查询模式的智能缓存

### AI助手集成考虑

**检索透明化要求：**
```
1. 查询意图识别 → 显示用户查询的理解结果
2. 检索路径记录 → 显示使用了哪些检索策略
3. 内容来源标注 → 标明信息来源和可信度
4. 计算过程展示 → 详细的定价计算步骤
5. 推荐逻辑说明 → 解释为什么推荐某些服务
```

**多模态展示支持：**
- **文本回答**：主要的回答内容
- **表格对比**：服务/价格对比表格
- **图表展示**：价格趋势、性能对比图表
- **代码示例**：API调用、配置示例
- **相关链接**：官方文档、详细说明链接

### 文件处理配置
**AI实施时的关键配置参数：**
- **源文件根路径**：`./current_prod_html/zh-cn/pricing/details/`
- **文件扫描模式**：递归扫描所有子目录
- **文件类型过滤**：`*.html`, `*.htm`
- **编码假设**：UTF-8（如有问题需要自动检测）
- **服务ID提取**：从目录名或文件名提取服务标识

### HTML文件特点分析
1. **统一模板结构**：所有文件应该基于相同或类似的模板生成
2. **中文内容为主**：需要重点处理中文文本的分词和清洗
3. **定价表格标准化**：Azure定价页面通常有标准化的表格结构
4. **服务分类明确**：通过目录结构可以初步确定服务分类

### 处理优先级策略
**基于Azure服务的商业重要性排序：**

**Tier 1 - 核心计算和存储（优先处理）**
- `virtual-machines/` - 虚拟机
- `storage/` - 存储服务
- `app-service/` - 应用服务
- `sql-database/` - SQL数据库

**Tier 2 - 重要基础服务**
- `cosmos-db/` - Cosmos数据库
- `virtual-network/` - 虚拟网络
- `load-balancer/` - 负载均衡器
- `cdn/` - 内容分发网络

**Tier 3 - 专业服务**
- `cognitive-services/` - 认知服务
- `functions/` - 无服务器函数
- `kubernetes-service/` - 容器服务
- `monitor/` - 监控服务

### 文件扫描和预处理要求
1. **递归扫描**：扫描 `details/` 下所有子目录的HTML文件
2. **文件完整性检查**：验证HTML文件是否完整和有效
3. **服务映射建立**：目录名 → 服务ID → 服务显示名称
4. **重复文件检测**：检测可能的重复或版本文件
5. **文件大小分析**：识别异常大小的文件（可能包含更多数据或存在问题）

### 混合智能RAG系统要求
**基于用户的混合RAG理念，平衡信息完整性与成本效率：**

**核心设计原则：**
1. **metadata驱动过滤**：通过丰富的产品metadata实现精准预过滤，大幅减少搜索空间
2. **智能向量化策略**：以产品/服务为单位进行有意义的embedding，避免盲目chunk切片
3. **多层级检索架构**：根据查询复杂度智能选择最优检索策略
4. **成本效率平衡**：动态选择传统RAG vs 大上下文方案，控制API调用成本
5. **信息损耗最小化**：在保持检索效率的同时，最大程度保持信息完整性

**检索策略分层：**
- **L1-快速过滤层**：metadata索引 + 关键词匹配（毫秒级响应）
- **L2-语义检索层**：产品级embedding + 向量相似度（100ms级响应）
- **L3-精准重排层**：rerank模型 + 相关性优化（500ms级响应）
- **L4-完整上下文层**：大窗口LLM + 完整信息（秒级响应，高精度场景）

**成本控制策略：**
- **查询分类路由**：简单查询走L1-L2，复杂查询走L3-L4
- **用户等级区分**：普通用户限制L4使用，VIP用户享受完整服务
- **缓存优化**：热点查询缓存，相似查询复用结果
- **token使用监控**：实时监控API调用成本，动态调整策略

**数据组织策略：**
- **产品维度完整性**：每个Azure服务保持完整的信息描述
- **功能维度细分**：核心功能、定价、使用场景等分别向量化
- **关联关系网络**：构建服务间的依赖和推荐关系
- **动态更新机制**：支持增量更新和版本管理

**AI助手集成要求：**
- **检索过程透明化**：详细记录检索路径和决策过程
- **计算步骤可视化**：定价计算的每个步骤都可追踪和解释
- **多模态交互支持**：文本、表格、图表等多种形式的信息展示
- **个性化推荐**：基于用户历史和偏好的智能推荐

---

**文档版本**：v1.4 - 阶段一优化版  
**最后更新**：2025-06-19  
**项目代号**：AzureCNArchaeologist  
**负责团队**：Azure定价重建项目组  
**特别说明**：集成阶段一优化方案的完整版本，注重实际可操作性和风险控制